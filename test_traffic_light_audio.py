#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys

class TrafficLightAudioTester:
    """红绿灯音频播放测试类"""
    
    def __init__(self):
        # 定义音频文件路径
        self.audio_files = {
            "路口1": "/home/<USER>/ucar_ws/my_code/voice/路口1.wav",
            "路口2": "/home/<USER>/ucar_ws/my_code/voice/路口2.wav"
        }
    
    def play_audio_file(self, audio_file_path, description="音频"):
        """播放指定的音频文件（模拟导航脚本中的函数）"""
        print("播放%s音频: %s" % (description, audio_file_path))
        print(f"【音频播放】: 播放{description}音频: {audio_file_path}")
        
        # 检查音频文件是否存在
        if os.path.exists(audio_file_path):
            try:
                # 使用aplay播放WAV文件
                result = os.system("aplay %s" % audio_file_path)
                if result == 0:
                    print("%s音频播放完成" % description)
                    print(f"【音频播放】: {description}音频播放完成")
                else:
                    print("%s音频播放失败，返回码: %d" % (description, result))
                    print(f"【音频播放】: {description}音频播放失败，返回码: {result}")
            except Exception as e:
                print("播放%s音频失败: %s" % (description, str(e)))
                print(f"【音频播放】: 播放{description}音频失败 - {str(e)}")
        else:
            print("%s音频文件不存在: %s" % (description, audio_file_path))
            print(f"【音频播放】: {description}音频文件不存在 - {audio_file_path}")
    
    def simulate_traffic_light_detection(self, traffic_light_number, is_green):
        """模拟红绿灯检测和音频播放逻辑"""
        print(f"\n===== 模拟红绿灯{traffic_light_number}检测 =====")
        print(f"红绿灯状态: {'绿灯' if is_green else '红灯'}")
        print("=" * 50)
        
        if traffic_light_number == 1:  # 红绿灯1
            if is_green:
                # 如果红绿灯1是绿灯，播放路口1音频，然后导航到路口1
                print("【决策】: 红绿灯1是绿灯，播放路口1音频并导航到路口1")
                
                # 播放路口1音频
                self.play_audio_file(self.audio_files["路口1"], "路口1")
                
                print("【导航】: 将导航到路口1并结束导航")
            else:
                # 如果红绿灯1不是绿灯（红灯），继续检测红绿灯2
                print("【决策】: 红绿灯1是红灯，将继续检测红绿灯2")
                print("【导航】: 将导航到红绿灯2")
        elif traffic_light_number == 2:  # 红绿灯2
            # 到达红绿灯2，播放路口2音频，然后导航到路口2
            print("【决策】: 到达红绿灯2，播放路口2音频并导航到路口2")
            
            # 播放路口2音频
            self.play_audio_file(self.audio_files["路口2"], "路口2")
            
            print("【导航】: 将导航到路口2并结束导航")
        
        print("=" * 50)
    
    def test_all_scenarios(self):
        """测试所有红绿灯场景"""
        print("===== 红绿灯音频播放功能测试 =====\n")
        
        # 检查音频文件是否存在
        print("1. 检查音频文件:")
        for name, path in self.audio_files.items():
            if os.path.exists(path):
                print(f"  ✓ {name}: {path}")
            else:
                print(f"  ✗ {name}: {path} (文件不存在)")
        
        print("\n2. 测试红绿灯检测场景:")
        
        # 场景1: 红绿灯1是绿灯
        print("\n场景1: 红绿灯1是绿灯")
        self.simulate_traffic_light_detection(1, True)
        input("按回车键继续下一个场景...")
        
        # 场景2: 红绿灯1是红灯，需要去红绿灯2
        print("\n场景2: 红绿灯1是红灯")
        self.simulate_traffic_light_detection(1, False)
        input("按回车键继续下一个场景...")
        
        # 场景3: 到达红绿灯2
        print("\n场景3: 到达红绿灯2")
        self.simulate_traffic_light_detection(2, True)  # 红绿灯2状态不重要，都会播放路口2音频
        
        print("\n===== 测试完成 =====")
    
    def test_single_audio(self, audio_name):
        """测试单个音频文件"""
        if audio_name in self.audio_files:
            audio_path = self.audio_files[audio_name]
            print(f"测试{audio_name}音频播放:")
            print("-" * 30)
            self.play_audio_file(audio_path, audio_name)
        else:
            print(f"未找到音频: {audio_name}")
            print(f"可用的音频: {list(self.audio_files.keys())}")

if __name__ == "__main__":
    tester = TrafficLightAudioTester()
    
    if len(sys.argv) > 1:
        # 如果提供了参数，测试指定的音频
        audio_name = sys.argv[1]
        tester.test_single_audio(audio_name)
    else:
        # 否则运行完整测试
        tester.test_all_scenarios()
