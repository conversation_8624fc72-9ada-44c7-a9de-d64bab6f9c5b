#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import subprocess

class IndependentLineFollowingTester:
    """独立线跟踪启动测试"""
    
    def __init__(self):
        self.line_following_script = "/home/<USER>/ucar_ws/my_code/run_line_following.sh"
        self.log_file = "/tmp/line_following.log"
    
    def check_script_exists(self):
        """检查线跟踪脚本是否存在"""
        print("🔍 检查线跟踪脚本...")
        if os.path.exists(self.line_following_script):
            print(f"✅ 脚本存在: {self.line_following_script}")
            
            # 检查执行权限
            if os.access(self.line_following_script, os.X_OK):
                print(f"✅ 脚本有执行权限")
                return True
            else:
                print(f"❌ 脚本没有执行权限")
                return False
        else:
            print(f"❌ 脚本不存在: {self.line_following_script}")
            return False
    
    def kill_existing_processes(self):
        """停止现有的线跟踪进程"""
        print("\n🛑 停止现有的线跟踪进程...")
        try:
            result = os.system("pkill -f run_line_following")
            if result == 0:
                print("✅ 已停止现有进程")
            else:
                print("ℹ️  没有找到现有进程")
            time.sleep(2)  # 等待进程完全停止
        except Exception as e:
            print(f"❌ 停止进程时出错: {e}")
    
    def start_line_following_independent(self):
        """使用nohup启动独立的线跟踪进程"""
        print("\n🚀 启动独立线跟踪进程...")
        
        # 构建命令：使用nohup确保进程独立运行
        cmd = f"cd /home/<USER>/ucar_ws && nohup {self.line_following_script} > {self.log_file} 2>&1 &"
        
        print(f"📝 执行命令: {cmd}")
        
        # 启动独立进程
        result = os.system(cmd)
        
        print(f"✅ 命令执行完成，返回码: {result}")
        print(f"📄 日志文件: {self.log_file}")
        
        # 等待进程启动
        print("⏳ 等待3秒让进程启动...")
        time.sleep(3)
        
        return result == 0
    
    def check_process_running(self):
        """检查线跟踪进程是否在运行"""
        print("\n🔍 检查线跟踪进程状态...")
        
        # 检查进程
        check_cmd = "ps aux | grep -v grep | grep run_line_following"
        result = os.system(check_cmd + " > /dev/null 2>&1")
        
        if result == 0:
            print("✅ 线跟踪进程正在运行")
            
            # 显示进程详情
            print("📊 进程详情:")
            os.system("ps aux | grep -v grep | grep run_line_following")
            return True
        else:
            print("❌ 未检测到线跟踪进程")
            return False
    
    def check_log_file(self):
        """检查日志文件"""
        print(f"\n📄 检查日志文件: {self.log_file}")
        
        if os.path.exists(self.log_file):
            print("✅ 日志文件存在")
            
            # 显示文件大小
            file_size = os.path.getsize(self.log_file)
            print(f"📊 文件大小: {file_size} 字节")
            
            if file_size > 0:
                print("📝 日志文件前10行:")
                os.system(f"head -10 {self.log_file}")
                
                print("\n📝 日志文件最后10行:")
                os.system(f"tail -10 {self.log_file}")
                return True
            else:
                print("⚠️  日志文件为空")
                return False
        else:
            print("❌ 日志文件不存在")
            return False
    
    def simulate_navigation_end(self):
        """模拟导航程序结束"""
        print("\n🎭 模拟导航程序结束...")
        print("   （在实际情况下，导航程序会在这里结束）")
        print("   线跟踪程序应该继续运行...")
        
        # 等待一段时间
        time.sleep(2)
        
        # 再次检查进程
        return self.check_process_running()
    
    def monitor_line_following(self, duration=10):
        """监控线跟踪程序运行"""
        print(f"\n👀 监控线跟踪程序运行 {duration} 秒...")
        
        start_time = time.time()
        while time.time() - start_time < duration:
            # 检查进程是否还在运行
            if not self.check_process_running():
                print("❌ 线跟踪进程已停止")
                return False
            
            print(f"⏱️  已运行 {int(time.time() - start_time)} 秒...")
            time.sleep(2)
        
        print("✅ 线跟踪程序持续运行正常")
        return True
    
    def show_control_commands(self):
        """显示控制命令"""
        print("\n🎮 线跟踪控制命令:")
        print(f"   查看实时日志: tail -f {self.log_file}")
        print(f"   停止线跟踪: pkill -f run_line_following")
        print(f"   检查进程: ps aux | grep run_line_following")
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 独立线跟踪启动测试")
        print("=" * 60)
        
        # 1. 检查脚本
        if not self.check_script_exists():
            print("❌ 测试失败：脚本不存在或没有权限")
            return False
        
        # 2. 停止现有进程
        self.kill_existing_processes()
        
        # 3. 启动独立进程
        if not self.start_line_following_independent():
            print("❌ 测试失败：无法启动线跟踪进程")
            return False
        
        # 4. 检查进程状态
        if not self.check_process_running():
            print("❌ 测试失败：线跟踪进程未运行")
            return False
        
        # 5. 检查日志文件
        self.check_log_file()
        
        # 6. 模拟导航结束
        if not self.simulate_navigation_end():
            print("❌ 测试失败：导航结束后线跟踪进程停止")
            return False
        
        # 7. 监控运行
        if not self.monitor_line_following(10):
            print("❌ 测试失败：线跟踪进程运行不稳定")
            return False
        
        # 8. 显示控制命令
        self.show_control_commands()
        
        print("\n" + "=" * 60)
        print("🎉 测试成功！独立线跟踪功能正常工作")
        print("=" * 60)
        
        # 询问是否停止线跟踪
        try:
            user_input = input("\n是否停止线跟踪程序？(y/N): ")
            if user_input.lower() in ['y', 'yes']:
                self.kill_existing_processes()
                print("✅ 线跟踪程序已停止")
            else:
                print("ℹ️  线跟踪程序继续运行")
                print(f"   查看日志: tail -f {self.log_file}")
                print(f"   停止程序: pkill -f run_line_following")
        except KeyboardInterrupt:
            print("\n\n⚠️  测试被中断")
            self.kill_existing_processes()
        
        return True

if __name__ == "__main__":
    tester = IndependentLineFollowingTester()
    tester.run_test()
