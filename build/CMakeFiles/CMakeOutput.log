The system is: Linux - 5.10.176 - aarch64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /usr/bin/cc 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"

The C compiler identification is GNU, found in "/home/<USER>/ucar_ws/build/CMakeFiles/3.13.4/CompilerIdC/a.out"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /usr/bin/c++ 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"

The CXX compiler identification is GNU, found in "/home/<USER>/ucar_ws/build/CMakeFiles/3.13.4/CompilerIdCXX/a.out"

Determining if the C compiler works passed with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_5ab0d/fast"
/usr/bin/make -f CMakeFiles/cmTC_5ab0d.dir/build.make CMakeFiles/cmTC_5ab0d.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_5ab0d.dir/testCCompiler.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_5ab0d.dir/testCCompiler.c.o   -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/testCCompiler.c
Linking C executable cmTC_5ab0d
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5ab0d.dir/link.txt --verbose=1
/usr/bin/cc      -rdynamic CMakeFiles/cmTC_5ab0d.dir/testCCompiler.c.o  -o cmTC_5ab0d 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


Detecting C compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_eb928/fast"
/usr/bin/make -f CMakeFiles/cmTC_eb928.dir/build.make CMakeFiles/cmTC_eb928.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_eb928.dir/CMakeCCompilerABI.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_eb928.dir/CMakeCCompilerABI.c.o   -c /usr/share/cmake-3.13/Modules/CMakeCCompilerABI.c
Linking C executable cmTC_eb928
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_eb928.dir/link.txt --verbose=1
/usr/bin/cc     -v -rdynamic CMakeFiles/cmTC_eb928.dir/CMakeCCompilerABI.c.o  -o cmTC_eb928 
Using built-in specs.
COLLECT_GCC=/usr/bin/cc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-linux-gnu/8/lto-wrapper
Target: aarch64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Debian 8.3.0-6' --with-bugurl=file:///usr/share/doc/gcc-8/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++ --prefix=/usr --with-gcc-major-version-only --program-suffix=-8 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --disable-libphobos --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu
Thread model: posix
gcc version 8.3.0 (Debian 8.3.0-6) 
COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/
LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_eb928' '-mlittle-endian' '-mabi=lp64'
 /usr/lib/gcc/aarch64-linux-gnu/8/collect2 -plugin /usr/lib/gcc/aarch64-linux-gnu/8/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/8/lto-wrapper -plugin-opt=-fresolution=/tmp/ccJXy4Mo.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -export-dynamic -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-843419 -pie -o cmTC_eb928 /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/Scrt1.o /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crti.o /usr/lib/gcc/aarch64-linux-gnu/8/crtbeginS.o -L/usr/lib/gcc/aarch64-linux-gnu/8 -L/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu -L/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib -L/lib/aarch64-linux-gnu -L/lib/../lib -L/usr/lib/aarch64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-linux-gnu/8/../../.. CMakeFiles/cmTC_eb928.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-linux-gnu/8/crtendS.o /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crtn.o
COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_eb928' '-mlittle-endian' '-mabi=lp64'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command:"/usr/bin/make" "cmTC_eb928/fast"]
  ignore line: [/usr/bin/make -f CMakeFiles/cmTC_eb928.dir/build.make CMakeFiles/cmTC_eb928.dir/build]
  ignore line: [make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp']
  ignore line: [Building C object CMakeFiles/cmTC_eb928.dir/CMakeCCompilerABI.c.o]
  ignore line: [/usr/bin/cc    -o CMakeFiles/cmTC_eb928.dir/CMakeCCompilerABI.c.o   -c /usr/share/cmake-3.13/Modules/CMakeCCompilerABI.c]
  ignore line: [Linking C executable cmTC_eb928]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_eb928.dir/link.txt --verbose=1]
  ignore line: [/usr/bin/cc     -v -rdynamic CMakeFiles/cmTC_eb928.dir/CMakeCCompilerABI.c.o  -o cmTC_eb928 ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/cc]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-linux-gnu/8/lto-wrapper]
  ignore line: [Target: aarch64-linux-gnu]
  ignore line: [Configured with: ../src/configure -v --with-pkgversion='Debian 8.3.0-6' --with-bugurl=file:///usr/share/doc/gcc-8/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++ --prefix=/usr --with-gcc-major-version-only --program-suffix=-8 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --disable-libphobos --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 8.3.0 (Debian 8.3.0-6) ]
  ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/]
  ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_eb928' '-mlittle-endian' '-mabi=lp64']
  link line: [ /usr/lib/gcc/aarch64-linux-gnu/8/collect2 -plugin /usr/lib/gcc/aarch64-linux-gnu/8/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/8/lto-wrapper -plugin-opt=-fresolution=/tmp/ccJXy4Mo.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr --hash-style=gnu -export-dynamic -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-843419 -pie -o cmTC_eb928 /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/Scrt1.o /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crti.o /usr/lib/gcc/aarch64-linux-gnu/8/crtbeginS.o -L/usr/lib/gcc/aarch64-linux-gnu/8 -L/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu -L/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib -L/lib/aarch64-linux-gnu -L/lib/../lib -L/usr/lib/aarch64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-linux-gnu/8/../../.. CMakeFiles/cmTC_eb928.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/aarch64-linux-gnu/8/crtendS.o /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crtn.o]
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/8/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccJXy4Mo.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [-export-dynamic] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib/ld-linux-aarch64.so.1] ==> ignore
    arg [-X] ==> ignore
    arg [-EL] ==> ignore
    arg [-maarch64linux] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [-pie] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_eb928] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/Scrt1.o] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crti.o] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/crtbeginS.o] ==> ignore
    arg [-L/usr/lib/gcc/aarch64-linux-gnu/8] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/8]
    arg [-L/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu]
    arg [-L/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib]
    arg [-L/lib/aarch64-linux-gnu] ==> dir [/lib/aarch64-linux-gnu]
    arg [-L/lib/../lib] ==> dir [/lib/../lib]
    arg [-L/usr/lib/aarch64-linux-gnu] ==> dir [/usr/lib/aarch64-linux-gnu]
    arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
    arg [-L/usr/lib/gcc/aarch64-linux-gnu/8/../../..] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../..]
    arg [CMakeFiles/cmTC_eb928.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-lgcc] ==> lib [gcc]
    arg [--push-state] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--pop-state] ==> ignore
    arg [-lc] ==> lib [c]
    arg [-lgcc] ==> lib [gcc]
    arg [--push-state] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--pop-state] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/crtendS.o] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crtn.o] ==> ignore
  collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/8] ==> [/usr/lib/gcc/aarch64-linux-gnu/8]
  collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu] ==> [/usr/lib/aarch64-linux-gnu]
  collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib] ==> [/usr/lib]
  collapse library dir [/lib/aarch64-linux-gnu] ==> [/lib/aarch64-linux-gnu]
  collapse library dir [/lib/../lib] ==> [/lib]
  collapse library dir [/usr/lib/aarch64-linux-gnu] ==> [/usr/lib/aarch64-linux-gnu]
  collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
  collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../..] ==> [/usr/lib]
  implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
  implicit dirs: [/usr/lib/gcc/aarch64-linux-gnu/8;/usr/lib/aarch64-linux-gnu;/usr/lib;/lib/aarch64-linux-gnu;/lib]
  implicit fwks: []




Detecting C [-std=c11] compiler features compiled with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_accdb/fast"
/usr/bin/make -f CMakeFiles/cmTC_accdb.dir/build.make CMakeFiles/cmTC_accdb.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_accdb.dir/feature_tests.c.o
/usr/bin/cc   -std=c11 -o CMakeFiles/cmTC_accdb.dir/feature_tests.c.o   -c /home/<USER>/ucar_ws/build/CMakeFiles/feature_tests.c
Linking C executable cmTC_accdb
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_accdb.dir/link.txt --verbose=1
/usr/bin/cc      -rdynamic CMakeFiles/cmTC_accdb.dir/feature_tests.c.o  -o cmTC_accdb 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:1c_restrict
    Feature record: C_FEATURE:1c_static_assert
    Feature record: C_FEATURE:1c_variadic_macros


Detecting C [-std=c99] compiler features compiled with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_1598c/fast"
/usr/bin/make -f CMakeFiles/cmTC_1598c.dir/build.make CMakeFiles/cmTC_1598c.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_1598c.dir/feature_tests.c.o
/usr/bin/cc   -std=c99 -o CMakeFiles/cmTC_1598c.dir/feature_tests.c.o   -c /home/<USER>/ucar_ws/build/CMakeFiles/feature_tests.c
Linking C executable cmTC_1598c
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_1598c.dir/link.txt --verbose=1
/usr/bin/cc      -rdynamic CMakeFiles/cmTC_1598c.dir/feature_tests.c.o  -o cmTC_1598c 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:1c_restrict
    Feature record: C_FEATURE:0c_static_assert
    Feature record: C_FEATURE:1c_variadic_macros


Detecting C [-std=c90] compiler features compiled with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_f0c59/fast"
/usr/bin/make -f CMakeFiles/cmTC_f0c59.dir/build.make CMakeFiles/cmTC_f0c59.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_f0c59.dir/feature_tests.c.o
/usr/bin/cc   -std=c90 -o CMakeFiles/cmTC_f0c59.dir/feature_tests.c.o   -c /home/<USER>/ucar_ws/build/CMakeFiles/feature_tests.c
Linking C executable cmTC_f0c59
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f0c59.dir/link.txt --verbose=1
/usr/bin/cc      -rdynamic CMakeFiles/cmTC_f0c59.dir/feature_tests.c.o  -o cmTC_f0c59 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:0c_restrict
    Feature record: C_FEATURE:0c_static_assert
    Feature record: C_FEATURE:0c_variadic_macros
Determining if the CXX compiler works passed with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_16fc8/fast"
/usr/bin/make -f CMakeFiles/cmTC_16fc8.dir/build.make CMakeFiles/cmTC_16fc8.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_16fc8.dir/testCXXCompiler.cxx.o
/usr/bin/c++     -o CMakeFiles/cmTC_16fc8.dir/testCXXCompiler.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/testCXXCompiler.cxx
Linking CXX executable cmTC_16fc8
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_16fc8.dir/link.txt --verbose=1
/usr/bin/c++       -rdynamic CMakeFiles/cmTC_16fc8.dir/testCXXCompiler.cxx.o  -o cmTC_16fc8 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_e69f2/fast"
/usr/bin/make -f CMakeFiles/cmTC_e69f2.dir/build.make CMakeFiles/cmTC_e69f2.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_e69f2.dir/CMakeCXXCompilerABI.cpp.o
/usr/bin/c++     -o CMakeFiles/cmTC_e69f2.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.13/Modules/CMakeCXXCompilerABI.cpp
Linking CXX executable cmTC_e69f2
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e69f2.dir/link.txt --verbose=1
/usr/bin/c++      -v -rdynamic CMakeFiles/cmTC_e69f2.dir/CMakeCXXCompilerABI.cpp.o  -o cmTC_e69f2 
Using built-in specs.
COLLECT_GCC=/usr/bin/c++
COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-linux-gnu/8/lto-wrapper
Target: aarch64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Debian 8.3.0-6' --with-bugurl=file:///usr/share/doc/gcc-8/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++ --prefix=/usr --with-gcc-major-version-only --program-suffix=-8 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --disable-libphobos --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu
Thread model: posix
gcc version 8.3.0 (Debian 8.3.0-6) 
COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/
LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_e69f2' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
 /usr/lib/gcc/aarch64-linux-gnu/8/collect2 -plugin /usr/lib/gcc/aarch64-linux-gnu/8/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/8/lto-wrapper -plugin-opt=-fresolution=/tmp/ccQk4Fcd.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -export-dynamic -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-843419 -pie -o cmTC_e69f2 /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/Scrt1.o /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crti.o /usr/lib/gcc/aarch64-linux-gnu/8/crtbeginS.o -L/usr/lib/gcc/aarch64-linux-gnu/8 -L/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu -L/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib -L/lib/aarch64-linux-gnu -L/lib/../lib -L/usr/lib/aarch64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-linux-gnu/8/../../.. CMakeFiles/cmTC_e69f2.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-linux-gnu/8/crtendS.o /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crtn.o
COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_e69f2' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command:"/usr/bin/make" "cmTC_e69f2/fast"]
  ignore line: [/usr/bin/make -f CMakeFiles/cmTC_e69f2.dir/build.make CMakeFiles/cmTC_e69f2.dir/build]
  ignore line: [make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp']
  ignore line: [Building CXX object CMakeFiles/cmTC_e69f2.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [/usr/bin/c++     -o CMakeFiles/cmTC_e69f2.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.13/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [Linking CXX executable cmTC_e69f2]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e69f2.dir/link.txt --verbose=1]
  ignore line: [/usr/bin/c++      -v -rdynamic CMakeFiles/cmTC_e69f2.dir/CMakeCXXCompilerABI.cpp.o  -o cmTC_e69f2 ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/c++]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/aarch64-linux-gnu/8/lto-wrapper]
  ignore line: [Target: aarch64-linux-gnu]
  ignore line: [Configured with: ../src/configure -v --with-pkgversion='Debian 8.3.0-6' --with-bugurl=file:///usr/share/doc/gcc-8/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++ --prefix=/usr --with-gcc-major-version-only --program-suffix=-8 --program-prefix=aarch64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-libquadmath --disable-libquadmath-support --enable-plugin --enable-default-pie --with-system-zlib --disable-libphobos --enable-multiarch --enable-fix-cortex-a53-843419 --disable-werror --enable-checking=release --build=aarch64-linux-gnu --host=aarch64-linux-gnu --target=aarch64-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 8.3.0 (Debian 8.3.0-6) ]
  ignore line: [COMPILER_PATH=/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/]
  ignore line: [LIBRARY_PATH=/usr/lib/gcc/aarch64-linux-gnu/8/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib/:/lib/aarch64-linux-gnu/:/lib/../lib/:/usr/lib/aarch64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/aarch64-linux-gnu/8/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_e69f2' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64']
  link line: [ /usr/lib/gcc/aarch64-linux-gnu/8/collect2 -plugin /usr/lib/gcc/aarch64-linux-gnu/8/liblto_plugin.so -plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/8/lto-wrapper -plugin-opt=-fresolution=/tmp/ccQk4Fcd.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr --hash-style=gnu -export-dynamic -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux --fix-cortex-a53-843419 -pie -o cmTC_e69f2 /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/Scrt1.o /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crti.o /usr/lib/gcc/aarch64-linux-gnu/8/crtbeginS.o -L/usr/lib/gcc/aarch64-linux-gnu/8 -L/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu -L/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib -L/lib/aarch64-linux-gnu -L/lib/../lib -L/usr/lib/aarch64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/aarch64-linux-gnu/8/../../.. CMakeFiles/cmTC_e69f2.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/aarch64-linux-gnu/8/crtendS.o /usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crtn.o]
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib/gcc/aarch64-linux-gnu/8/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccQk4Fcd.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [-export-dynamic] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib/ld-linux-aarch64.so.1] ==> ignore
    arg [-X] ==> ignore
    arg [-EL] ==> ignore
    arg [-maarch64linux] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [-pie] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_e69f2] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/Scrt1.o] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crti.o] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/crtbeginS.o] ==> ignore
    arg [-L/usr/lib/gcc/aarch64-linux-gnu/8] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/8]
    arg [-L/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu]
    arg [-L/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib]
    arg [-L/lib/aarch64-linux-gnu] ==> dir [/lib/aarch64-linux-gnu]
    arg [-L/lib/../lib] ==> dir [/lib/../lib]
    arg [-L/usr/lib/aarch64-linux-gnu] ==> dir [/usr/lib/aarch64-linux-gnu]
    arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
    arg [-L/usr/lib/gcc/aarch64-linux-gnu/8/../../..] ==> dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../..]
    arg [CMakeFiles/cmTC_e69f2.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lm] ==> lib [m]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lc] ==> lib [c]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/crtendS.o] ==> ignore
    arg [/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu/crtn.o] ==> ignore
  collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/8] ==> [/usr/lib/gcc/aarch64-linux-gnu/8]
  collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../../aarch64-linux-gnu] ==> [/usr/lib/aarch64-linux-gnu]
  collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../../../lib] ==> [/usr/lib]
  collapse library dir [/lib/aarch64-linux-gnu] ==> [/lib/aarch64-linux-gnu]
  collapse library dir [/lib/../lib] ==> [/lib]
  collapse library dir [/usr/lib/aarch64-linux-gnu] ==> [/usr/lib/aarch64-linux-gnu]
  collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
  collapse library dir [/usr/lib/gcc/aarch64-linux-gnu/8/../../..] ==> [/usr/lib]
  implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
  implicit dirs: [/usr/lib/gcc/aarch64-linux-gnu/8;/usr/lib/aarch64-linux-gnu;/usr/lib;/lib/aarch64-linux-gnu;/lib]
  implicit fwks: []




Detecting CXX [-std=c++2a] compiler features compiled with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_f74f5/fast"
/usr/bin/make -f CMakeFiles/cmTC_f74f5.dir/build.make CMakeFiles/cmTC_f74f5.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_f74f5.dir/feature_tests.cxx.o
/usr/bin/c++    -std=c++2a -o CMakeFiles/cmTC_f74f5.dir/feature_tests.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_f74f5
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f74f5.dir/link.txt --verbose=1
/usr/bin/c++       -rdynamic CMakeFiles/cmTC_f74f5.dir/feature_tests.cxx.o  -o cmTC_f74f5 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


    Feature record: CXX_FEATURE:1cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:1cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:1cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:1cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:1cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:1cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:1cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:1cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:1cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:1cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:1cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++17] compiler features compiled with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_f4c87/fast"
/usr/bin/make -f CMakeFiles/cmTC_f4c87.dir/build.make CMakeFiles/cmTC_f4c87.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_f4c87.dir/feature_tests.cxx.o
/usr/bin/c++    -std=c++17 -o CMakeFiles/cmTC_f4c87.dir/feature_tests.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_f4c87
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f4c87.dir/link.txt --verbose=1
/usr/bin/c++       -rdynamic CMakeFiles/cmTC_f4c87.dir/feature_tests.cxx.o  -o cmTC_f4c87 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


    Feature record: CXX_FEATURE:1cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:1cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:1cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:1cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:1cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:1cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:1cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:1cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:1cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:1cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:1cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++14] compiler features compiled with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_ee8d0/fast"
/usr/bin/make -f CMakeFiles/cmTC_ee8d0.dir/build.make CMakeFiles/cmTC_ee8d0.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_ee8d0.dir/feature_tests.cxx.o
/usr/bin/c++    -std=c++14 -o CMakeFiles/cmTC_ee8d0.dir/feature_tests.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_ee8d0
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ee8d0.dir/link.txt --verbose=1
/usr/bin/c++       -rdynamic CMakeFiles/cmTC_ee8d0.dir/feature_tests.cxx.o  -o cmTC_ee8d0 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


    Feature record: CXX_FEATURE:1cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:1cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:1cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:1cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:1cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:1cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:1cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:1cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:1cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:1cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:1cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++11] compiler features compiled with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_39b8f/fast"
/usr/bin/make -f CMakeFiles/cmTC_39b8f.dir/build.make CMakeFiles/cmTC_39b8f.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_39b8f.dir/feature_tests.cxx.o
/usr/bin/c++    -std=c++11 -o CMakeFiles/cmTC_39b8f.dir/feature_tests.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_39b8f
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_39b8f.dir/link.txt --verbose=1
/usr/bin/c++       -rdynamic CMakeFiles/cmTC_39b8f.dir/feature_tests.cxx.o  -o cmTC_39b8f 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


    Feature record: CXX_FEATURE:0cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:0cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:0cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:0cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:0cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:0cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:0cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:0cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:0cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:0cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:0cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++98] compiler features compiled with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_e019b/fast"
/usr/bin/make -f CMakeFiles/cmTC_e019b.dir/build.make CMakeFiles/cmTC_e019b.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_e019b.dir/feature_tests.cxx.o
/usr/bin/c++    -std=c++98 -o CMakeFiles/cmTC_e019b.dir/feature_tests.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/feature_tests.cxx
Linking CXX executable cmTC_e019b
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e019b.dir/link.txt --verbose=1
/usr/bin/c++       -rdynamic CMakeFiles/cmTC_e019b.dir/feature_tests.cxx.o  -o cmTC_e019b 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


    Feature record: CXX_FEATURE:0cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:0cxx_alias_templates
    Feature record: CXX_FEATURE:0cxx_alignas
    Feature record: CXX_FEATURE:0cxx_alignof
    Feature record: CXX_FEATURE:0cxx_attributes
    Feature record: CXX_FEATURE:0cxx_attribute_deprecated
    Feature record: CXX_FEATURE:0cxx_auto_type
    Feature record: CXX_FEATURE:0cxx_binary_literals
    Feature record: CXX_FEATURE:0cxx_constexpr
    Feature record: CXX_FEATURE:0cxx_contextual_conversions
    Feature record: CXX_FEATURE:0cxx_decltype
    Feature record: CXX_FEATURE:0cxx_decltype_auto
    Feature record: CXX_FEATURE:0cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:0cxx_default_function_template_args
    Feature record: CXX_FEATURE:0cxx_defaulted_functions
    Feature record: CXX_FEATURE:0cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:0cxx_delegating_constructors
    Feature record: CXX_FEATURE:0cxx_deleted_functions
    Feature record: CXX_FEATURE:0cxx_digit_separators
    Feature record: CXX_FEATURE:0cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:0cxx_explicit_conversions
    Feature record: CXX_FEATURE:0cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:0cxx_extern_templates
    Feature record: CXX_FEATURE:0cxx_final
    Feature record: CXX_FEATURE:0cxx_func_identifier
    Feature record: CXX_FEATURE:0cxx_generalized_initializers
    Feature record: CXX_FEATURE:0cxx_generic_lambdas
    Feature record: CXX_FEATURE:0cxx_inheriting_constructors
    Feature record: CXX_FEATURE:0cxx_inline_namespaces
    Feature record: CXX_FEATURE:0cxx_lambdas
    Feature record: CXX_FEATURE:0cxx_lambda_init_captures
    Feature record: CXX_FEATURE:0cxx_local_type_template_args
    Feature record: CXX_FEATURE:0cxx_long_long_type
    Feature record: CXX_FEATURE:0cxx_noexcept
    Feature record: CXX_FEATURE:0cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:0cxx_nullptr
    Feature record: CXX_FEATURE:0cxx_override
    Feature record: CXX_FEATURE:0cxx_range_for
    Feature record: CXX_FEATURE:0cxx_raw_string_literals
    Feature record: CXX_FEATURE:0cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:0cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:0cxx_return_type_deduction
    Feature record: CXX_FEATURE:0cxx_right_angle_brackets
    Feature record: CXX_FEATURE:0cxx_rvalue_references
    Feature record: CXX_FEATURE:0cxx_sizeof_member
    Feature record: CXX_FEATURE:0cxx_static_assert
    Feature record: CXX_FEATURE:0cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:0cxx_thread_local
    Feature record: CXX_FEATURE:0cxx_trailing_return_types
    Feature record: CXX_FEATURE:0cxx_unicode_literals
    Feature record: CXX_FEATURE:0cxx_uniform_initialization
    Feature record: CXX_FEATURE:0cxx_unrestricted_unions
    Feature record: CXX_FEATURE:0cxx_user_literals
    Feature record: CXX_FEATURE:0cxx_variable_templates
    Feature record: CXX_FEATURE:0cxx_variadic_macros
    Feature record: CXX_FEATURE:0cxx_variadic_templates
Determining if the include file pthread.h exists passed with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_6a26c/fast"
/usr/bin/make -f CMakeFiles/cmTC_6a26c.dir/build.make CMakeFiles/cmTC_6a26c.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_6a26c.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_6a26c.dir/CheckIncludeFile.c.o   -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_6a26c
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_6a26c.dir/link.txt --verbose=1
/usr/bin/cc      -rdynamic CMakeFiles/cmTC_6a26c.dir/CheckIncludeFile.c.o  -o cmTC_6a26c 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


Determining if the function pthread_create exists in the pthread passed with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_c054f/fast"
/usr/bin/make -f CMakeFiles/cmTC_c054f.dir/build.make CMakeFiles/cmTC_c054f.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_c054f.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_c054f.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.13/Modules/CheckFunctionExists.c
Linking C executable cmTC_c054f
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c054f.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    -rdynamic CMakeFiles/cmTC_c054f.dir/CheckFunctionExists.c.o  -o cmTC_c054f -lpthread 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


Performing C++ SOURCE FILE Test COMPILER_SUPPORTS_CXX11 succeeded with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_f1a68/fast"
/usr/bin/make -f CMakeFiles/cmTC_f1a68.dir/build.make CMakeFiles/cmTC_f1a68.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_f1a68.dir/src.cxx.o
/usr/bin/c++    -DCOMPILER_SUPPORTS_CXX11   -std=c++11 -o CMakeFiles/cmTC_f1a68.dir/src.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_f1a68
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f1a68.dir/link.txt --verbose=1
/usr/bin/c++   -DCOMPILER_SUPPORTS_CXX11    -rdynamic CMakeFiles/cmTC_f1a68.dir/src.cxx.o  -o cmTC_f1a68 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

Source file was:
int main() { return 0; }
Determining if the include file unistd.h exists passed with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_8026f/fast"
/usr/bin/make -f CMakeFiles/cmTC_8026f.dir/build.make CMakeFiles/cmTC_8026f.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_8026f.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_8026f.dir/CheckIncludeFile.c.o   -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_8026f
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8026f.dir/link.txt --verbose=1
/usr/bin/cc      -rdynamic CMakeFiles/cmTC_8026f.dir/CheckIncludeFile.c.o  -o cmTC_8026f 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


Determining if the drand48 exist passed with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_9ff97/fast"
/usr/bin/make -f CMakeFiles/cmTC_9ff97.dir/build.make CMakeFiles/cmTC_9ff97.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_9ff97.dir/CheckSymbolExists.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_9ff97.dir/CheckSymbolExists.c.o   -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/CheckSymbolExists.c
Linking C executable cmTC_9ff97
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_9ff97.dir/link.txt --verbose=1
/usr/bin/cc      -rdynamic CMakeFiles/cmTC_9ff97.dir/CheckSymbolExists.c.o  -o cmTC_9ff97 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

File /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/CheckSymbolExists.c:
/* */
#include <stdlib.h>

int main(int argc, char** argv)
{
  (void)argv;
#ifndef drand48
  return ((int*)(&drand48))[argc];
#else
  (void)argc;
  return 0;
#endif
}

Determining if the include file sys/time.h exists passed with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_3f660/fast"
/usr/bin/make -f CMakeFiles/cmTC_3f660.dir/build.make CMakeFiles/cmTC_3f660.dir/build
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_3f660.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_3f660.dir/CheckIncludeFile.c.o   -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_3f660
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_3f660.dir/link.txt --verbose=1
/usr/bin/cc      -rdynamic CMakeFiles/cmTC_3f660.dir/CheckIncludeFile.c.o  -o cmTC_3f660 
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'


Performing C++ SOURCE FILE Test COMPILER_SUPPORTS_CXX11 succeeded with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_31480/fast"
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
/usr/bin/make -f CMakeFiles/cmTC_31480.dir/build.make CMakeFiles/cmTC_31480.dir/build
make[2]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_31480.dir/src.cxx.o
/usr/bin/c++    -DCOMPILER_SUPPORTS_CXX11   -std=c++11 -o CMakeFiles/cmTC_31480.dir/src.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_31480
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_31480.dir/link.txt --verbose=1
/usr/bin/c++   -DCOMPILER_SUPPORTS_CXX11    -rdynamic CMakeFiles/cmTC_31480.dir/src.cxx.o  -o cmTC_31480 
make[2]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

Source file was:
int main() { return 0; }
Performing C++ SOURCE FILE Test COMPILER_SUPPORTS_CXX11 succeeded with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_bfc8a/fast"
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
/usr/bin/make -f CMakeFiles/cmTC_bfc8a.dir/build.make CMakeFiles/cmTC_bfc8a.dir/build
make[2]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_bfc8a.dir/src.cxx.o
/usr/bin/c++    -DCOMPILER_SUPPORTS_CXX11   -std=c++11 -o CMakeFiles/cmTC_bfc8a.dir/src.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_bfc8a
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_bfc8a.dir/link.txt --verbose=1
/usr/bin/c++   -DCOMPILER_SUPPORTS_CXX11    -rdynamic CMakeFiles/cmTC_bfc8a.dir/src.cxx.o  -o cmTC_bfc8a 
make[2]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

Source file was:
int main() { return 0; }
Performing C++ SOURCE FILE Test COMPILER_SUPPORTS_CXX11 succeeded with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_e6203/fast"
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
/usr/bin/make -f CMakeFiles/cmTC_e6203.dir/build.make CMakeFiles/cmTC_e6203.dir/build
make[2]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_e6203.dir/src.cxx.o
/usr/bin/c++    -DCOMPILER_SUPPORTS_CXX11   -std=c++11 -o CMakeFiles/cmTC_e6203.dir/src.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_e6203
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e6203.dir/link.txt --verbose=1
/usr/bin/c++   -DCOMPILER_SUPPORTS_CXX11    -rdynamic CMakeFiles/cmTC_e6203.dir/src.cxx.o  -o cmTC_e6203 
make[2]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

Source file was:
int main() { return 0; }
Performing C++ SOURCE FILE Test COMPILER_SUPPORTS_CXX11 succeeded with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_15b16/fast"
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
/usr/bin/make -f CMakeFiles/cmTC_15b16.dir/build.make CMakeFiles/cmTC_15b16.dir/build
make[2]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_15b16.dir/src.cxx.o
/usr/bin/c++    -DCOMPILER_SUPPORTS_CXX11   -std=c++11 -o CMakeFiles/cmTC_15b16.dir/src.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_15b16
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_15b16.dir/link.txt --verbose=1
/usr/bin/c++   -DCOMPILER_SUPPORTS_CXX11    -rdynamic CMakeFiles/cmTC_15b16.dir/src.cxx.o  -o cmTC_15b16 
make[2]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

Source file was:
int main() { return 0; }
Performing C++ SOURCE FILE Test COMPILER_SUPPORTS_CXX11 succeeded with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_a2bf5/fast"
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
/usr/bin/make -f CMakeFiles/cmTC_a2bf5.dir/build.make CMakeFiles/cmTC_a2bf5.dir/build
make[2]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_a2bf5.dir/src.cxx.o
/usr/bin/c++    -DCOMPILER_SUPPORTS_CXX11   -std=c++11 -o CMakeFiles/cmTC_a2bf5.dir/src.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_a2bf5
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_a2bf5.dir/link.txt --verbose=1
/usr/bin/c++   -DCOMPILER_SUPPORTS_CXX11    -rdynamic CMakeFiles/cmTC_a2bf5.dir/src.cxx.o  -o cmTC_a2bf5 
make[2]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

Source file was:
int main() { return 0; }
Performing C++ SOURCE FILE Test COMPILER_SUPPORTS_CXX11 succeeded with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_62a52/fast"
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
/usr/bin/make -f CMakeFiles/cmTC_62a52.dir/build.make CMakeFiles/cmTC_62a52.dir/build
make[2]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_62a52.dir/src.cxx.o
/usr/bin/c++    -DCOMPILER_SUPPORTS_CXX11   -std=c++11 -o CMakeFiles/cmTC_62a52.dir/src.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_62a52
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_62a52.dir/link.txt --verbose=1
/usr/bin/c++   -DCOMPILER_SUPPORTS_CXX11    -rdynamic CMakeFiles/cmTC_62a52.dir/src.cxx.o  -o cmTC_62a52 
make[2]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

Source file was:
int main() { return 0; }
Performing C++ SOURCE FILE Test COMPILER_SUPPORTS_CXX11 succeeded with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_31a3e/fast"
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
/usr/bin/make -f CMakeFiles/cmTC_31a3e.dir/build.make CMakeFiles/cmTC_31a3e.dir/build
make[2]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_31a3e.dir/src.cxx.o
/usr/bin/c++    -DCOMPILER_SUPPORTS_CXX11   -std=c++11 -o CMakeFiles/cmTC_31a3e.dir/src.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_31a3e
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_31a3e.dir/link.txt --verbose=1
/usr/bin/c++   -DCOMPILER_SUPPORTS_CXX11    -rdynamic CMakeFiles/cmTC_31a3e.dir/src.cxx.o  -o cmTC_31a3e 
make[2]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

Source file was:
int main() { return 0; }
Performing C++ SOURCE FILE Test COMPILER_SUPPORTS_CXX11 succeeded with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_a1c56/fast"
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
/usr/bin/make -f CMakeFiles/cmTC_a1c56.dir/build.make CMakeFiles/cmTC_a1c56.dir/build
make[2]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_a1c56.dir/src.cxx.o
/usr/bin/c++    -DCOMPILER_SUPPORTS_CXX11   -std=c++11 -o CMakeFiles/cmTC_a1c56.dir/src.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_a1c56
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_a1c56.dir/link.txt --verbose=1
/usr/bin/c++   -DCOMPILER_SUPPORTS_CXX11    -rdynamic CMakeFiles/cmTC_a1c56.dir/src.cxx.o  -o cmTC_a1c56 
make[2]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

Source file was:
int main() { return 0; }
Performing C++ SOURCE FILE Test COMPILER_SUPPORTS_CXX11 succeeded with the following output:
Change Dir: /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_7598f/fast"
make[1]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
/usr/bin/make -f CMakeFiles/cmTC_7598f.dir/build.make CMakeFiles/cmTC_7598f.dir/build
make[2]: Entering directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_7598f.dir/src.cxx.o
/usr/bin/c++    -DCOMPILER_SUPPORTS_CXX11   -std=c++11 -o CMakeFiles/cmTC_7598f.dir/src.cxx.o -c /home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp/src.cxx
Linking CXX executable cmTC_7598f
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7598f.dir/link.txt --verbose=1
/usr/bin/c++   -DCOMPILER_SUPPORTS_CXX11    -rdynamic CMakeFiles/cmTC_7598f.dir/src.cxx.o  -o cmTC_7598f 
make[2]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'
make[1]: Leaving directory '/home/<USER>/ucar_ws/build/CMakeFiles/CMakeTmp'

Source file was:
int main() { return 0; }
