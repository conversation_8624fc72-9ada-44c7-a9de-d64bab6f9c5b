# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# The main recursive all target
all:

.PHONY : all

# The main recursive preinstall target
preinstall:

.PHONY : preinstall

# The main recursive clean target
clean:

.PHONY : clean

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ucar_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ucar_ws/build

#=============================================================================
# Target rules for target CMakeFiles/tests.dir

# All Build rule for target.
CMakeFiles/tests.dir/all:
	$(MAKE) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/depend
	$(MAKE) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target tests"
.PHONY : CMakeFiles/tests.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : CMakeFiles/tests.dir/rule

# Convenience name for target.
tests: CMakeFiles/tests.dir/rule

.PHONY : tests

# clean rule for target.
CMakeFiles/tests.dir/clean:
	$(MAKE) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/clean
.PHONY : CMakeFiles/tests.dir/clean

# clean rule for target.
clean: CMakeFiles/tests.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/download_extra_data.dir

# All Build rule for target.
CMakeFiles/download_extra_data.dir/all:
	$(MAKE) -f CMakeFiles/download_extra_data.dir/build.make CMakeFiles/download_extra_data.dir/depend
	$(MAKE) -f CMakeFiles/download_extra_data.dir/build.make CMakeFiles/download_extra_data.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target download_extra_data"
.PHONY : CMakeFiles/download_extra_data.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/download_extra_data.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/download_extra_data.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : CMakeFiles/download_extra_data.dir/rule

# Convenience name for target.
download_extra_data: CMakeFiles/download_extra_data.dir/rule

.PHONY : download_extra_data

# clean rule for target.
CMakeFiles/download_extra_data.dir/clean:
	$(MAKE) -f CMakeFiles/download_extra_data.dir/build.make CMakeFiles/download_extra_data.dir/clean
.PHONY : CMakeFiles/download_extra_data.dir/clean

# clean rule for target.
clean: CMakeFiles/download_extra_data.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/run_tests.dir

# All Build rule for target.
CMakeFiles/run_tests.dir/all:
	$(MAKE) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/depend
	$(MAKE) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target run_tests"
.PHONY : CMakeFiles/run_tests.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/run_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/run_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : CMakeFiles/run_tests.dir/rule

# Convenience name for target.
run_tests: CMakeFiles/run_tests.dir/rule

.PHONY : run_tests

# clean rule for target.
CMakeFiles/run_tests.dir/clean:
	$(MAKE) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/clean
.PHONY : CMakeFiles/run_tests.dir/clean

# clean rule for target.
clean: CMakeFiles/run_tests.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/clean_test_results.dir

# All Build rule for target.
CMakeFiles/clean_test_results.dir/all:
	$(MAKE) -f CMakeFiles/clean_test_results.dir/build.make CMakeFiles/clean_test_results.dir/depend
	$(MAKE) -f CMakeFiles/clean_test_results.dir/build.make CMakeFiles/clean_test_results.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target clean_test_results"
.PHONY : CMakeFiles/clean_test_results.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/clean_test_results.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/clean_test_results.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : CMakeFiles/clean_test_results.dir/rule

# Convenience name for target.
clean_test_results: CMakeFiles/clean_test_results.dir/rule

.PHONY : clean_test_results

# clean rule for target.
CMakeFiles/clean_test_results.dir/clean:
	$(MAKE) -f CMakeFiles/clean_test_results.dir/build.make CMakeFiles/clean_test_results.dir/clean
.PHONY : CMakeFiles/clean_test_results.dir/clean

# clean rule for target.
clean: CMakeFiles/clean_test_results.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/doxygen.dir

# All Build rule for target.
CMakeFiles/doxygen.dir/all:
	$(MAKE) -f CMakeFiles/doxygen.dir/build.make CMakeFiles/doxygen.dir/depend
	$(MAKE) -f CMakeFiles/doxygen.dir/build.make CMakeFiles/doxygen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target doxygen"
.PHONY : CMakeFiles/doxygen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/doxygen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/doxygen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : CMakeFiles/doxygen.dir/rule

# Convenience name for target.
doxygen: CMakeFiles/doxygen.dir/rule

.PHONY : doxygen

# clean rule for target.
CMakeFiles/doxygen.dir/clean:
	$(MAKE) -f CMakeFiles/doxygen.dir/build.make CMakeFiles/doxygen.dir/clean
.PHONY : CMakeFiles/doxygen.dir/clean

# clean rule for target.
clean: CMakeFiles/doxygen.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory gtest

# Convenience name for "all" pass in the directory.
gtest/all: gtest/googlemock/all

.PHONY : gtest/all

# Convenience name for "clean" pass in the directory.
gtest/clean: gtest/googlemock/clean

.PHONY : gtest/clean

# Convenience name for "preinstall" pass in the directory.
gtest/preinstall: gtest/googlemock/preinstall

.PHONY : gtest/preinstall

#=============================================================================
# Directory level rules for directory gtest/googlemock

# Convenience name for "all" pass in the directory.
gtest/googlemock/all: gtest/googlemock/gtest/all

.PHONY : gtest/googlemock/all

# Convenience name for "clean" pass in the directory.
gtest/googlemock/clean: gtest/googlemock/CMakeFiles/gmock_main.dir/clean
gtest/googlemock/clean: gtest/googlemock/CMakeFiles/gmock.dir/clean
gtest/googlemock/clean: gtest/googlemock/gtest/clean

.PHONY : gtest/googlemock/clean

# Convenience name for "preinstall" pass in the directory.
gtest/googlemock/preinstall: gtest/googlemock/gtest/preinstall

.PHONY : gtest/googlemock/preinstall

#=============================================================================
# Target rules for target gtest/googlemock/CMakeFiles/gmock_main.dir

# All Build rule for target.
gtest/googlemock/CMakeFiles/gmock_main.dir/all: gtest/googlemock/CMakeFiles/gmock.dir/all
gtest/googlemock/CMakeFiles/gmock_main.dir/all: gtest/googlemock/gtest/CMakeFiles/gtest.dir/all
	$(MAKE) -f gtest/googlemock/CMakeFiles/gmock_main.dir/build.make gtest/googlemock/CMakeFiles/gmock_main.dir/depend
	$(MAKE) -f gtest/googlemock/CMakeFiles/gmock_main.dir/build.make gtest/googlemock/CMakeFiles/gmock_main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=12,13 "Built target gmock_main"
.PHONY : gtest/googlemock/CMakeFiles/gmock_main.dir/all

# Build rule for subdir invocation for target.
gtest/googlemock/CMakeFiles/gmock_main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 6
	$(MAKE) -f CMakeFiles/Makefile2 gtest/googlemock/CMakeFiles/gmock_main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : gtest/googlemock/CMakeFiles/gmock_main.dir/rule

# Convenience name for target.
gmock_main: gtest/googlemock/CMakeFiles/gmock_main.dir/rule

.PHONY : gmock_main

# clean rule for target.
gtest/googlemock/CMakeFiles/gmock_main.dir/clean:
	$(MAKE) -f gtest/googlemock/CMakeFiles/gmock_main.dir/build.make gtest/googlemock/CMakeFiles/gmock_main.dir/clean
.PHONY : gtest/googlemock/CMakeFiles/gmock_main.dir/clean

# clean rule for target.
clean: gtest/googlemock/CMakeFiles/gmock_main.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target gtest/googlemock/CMakeFiles/gmock.dir

# All Build rule for target.
gtest/googlemock/CMakeFiles/gmock.dir/all: gtest/googlemock/gtest/CMakeFiles/gtest.dir/all
	$(MAKE) -f gtest/googlemock/CMakeFiles/gmock.dir/build.make gtest/googlemock/CMakeFiles/gmock.dir/depend
	$(MAKE) -f gtest/googlemock/CMakeFiles/gmock.dir/build.make gtest/googlemock/CMakeFiles/gmock.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=10,11 "Built target gmock"
.PHONY : gtest/googlemock/CMakeFiles/gmock.dir/all

# Build rule for subdir invocation for target.
gtest/googlemock/CMakeFiles/gmock.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 4
	$(MAKE) -f CMakeFiles/Makefile2 gtest/googlemock/CMakeFiles/gmock.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : gtest/googlemock/CMakeFiles/gmock.dir/rule

# Convenience name for target.
gmock: gtest/googlemock/CMakeFiles/gmock.dir/rule

.PHONY : gmock

# clean rule for target.
gtest/googlemock/CMakeFiles/gmock.dir/clean:
	$(MAKE) -f gtest/googlemock/CMakeFiles/gmock.dir/build.make gtest/googlemock/CMakeFiles/gmock.dir/clean
.PHONY : gtest/googlemock/CMakeFiles/gmock.dir/clean

# clean rule for target.
clean: gtest/googlemock/CMakeFiles/gmock.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory gtest/googlemock/gtest

# Convenience name for "all" pass in the directory.
gtest/googlemock/gtest/all:

.PHONY : gtest/googlemock/gtest/all

# Convenience name for "clean" pass in the directory.
gtest/googlemock/gtest/clean: gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/clean
gtest/googlemock/gtest/clean: gtest/googlemock/gtest/CMakeFiles/gtest.dir/clean

.PHONY : gtest/googlemock/gtest/clean

# Convenience name for "preinstall" pass in the directory.
gtest/googlemock/gtest/preinstall:

.PHONY : gtest/googlemock/gtest/preinstall

#=============================================================================
# Target rules for target gtest/googlemock/gtest/CMakeFiles/gtest_main.dir

# All Build rule for target.
gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/all: gtest/googlemock/gtest/CMakeFiles/gtest.dir/all
	$(MAKE) -f gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/build.make gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/depend
	$(MAKE) -f gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/build.make gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=16,17 "Built target gtest_main"
.PHONY : gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/all

# Build rule for subdir invocation for target.
gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 4
	$(MAKE) -f CMakeFiles/Makefile2 gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/rule

# Convenience name for target.
gtest_main: gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/rule

.PHONY : gtest_main

# clean rule for target.
gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/clean:
	$(MAKE) -f gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/build.make gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/clean
.PHONY : gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/clean

# clean rule for target.
clean: gtest/googlemock/gtest/CMakeFiles/gtest_main.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target gtest/googlemock/gtest/CMakeFiles/gtest.dir

# All Build rule for target.
gtest/googlemock/gtest/CMakeFiles/gtest.dir/all:
	$(MAKE) -f gtest/googlemock/gtest/CMakeFiles/gtest.dir/build.make gtest/googlemock/gtest/CMakeFiles/gtest.dir/depend
	$(MAKE) -f gtest/googlemock/gtest/CMakeFiles/gtest.dir/build.make gtest/googlemock/gtest/CMakeFiles/gtest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=14,15 "Built target gtest"
.PHONY : gtest/googlemock/gtest/CMakeFiles/gtest.dir/all

# Build rule for subdir invocation for target.
gtest/googlemock/gtest/CMakeFiles/gtest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 gtest/googlemock/gtest/CMakeFiles/gtest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : gtest/googlemock/gtest/CMakeFiles/gtest.dir/rule

# Convenience name for target.
gtest: gtest/googlemock/gtest/CMakeFiles/gtest.dir/rule

.PHONY : gtest

# clean rule for target.
gtest/googlemock/gtest/CMakeFiles/gtest.dir/clean:
	$(MAKE) -f gtest/googlemock/gtest/CMakeFiles/gtest.dir/build.make gtest/googlemock/gtest/CMakeFiles/gtest.dir/clean
.PHONY : gtest/googlemock/gtest/CMakeFiles/gtest.dir/clean

# clean rule for target.
clean: gtest/googlemock/gtest/CMakeFiles/gtest.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory speech_command

# Convenience name for "all" pass in the directory.
speech_command/all: speech_command/CMakeFiles/audio_player.dir/all
speech_command/all: speech_command/CMakeFiles/simple_speech_command_node.dir/all
speech_command/all: speech_command/CMakeFiles/audio_recorder.dir/all
speech_command/all: speech_command/CMakeFiles/speech_command_node.dir/all
speech_command/all: speech_command/CMakeFiles/AIUITester.dir/all

.PHONY : speech_command/all

# Convenience name for "clean" pass in the directory.
speech_command/clean: speech_command/CMakeFiles/audio_player.dir/clean
speech_command/clean: speech_command/CMakeFiles/simple_speech_command_node.dir/clean
speech_command/clean: speech_command/CMakeFiles/roscpp_generate_messages_py.dir/clean
speech_command/clean: speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/clean
speech_command/clean: speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/clean
speech_command/clean: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/clean
speech_command/clean: speech_command/CMakeFiles/audio_recorder.dir/clean
speech_command/clean: speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/clean
speech_command/clean: speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/clean
speech_command/clean: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/clean
speech_command/clean: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/clean
speech_command/clean: speech_command/CMakeFiles/speech_command_node.dir/clean
speech_command/clean: speech_command/CMakeFiles/AIUITester.dir/clean
speech_command/clean: speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/clean
speech_command/clean: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/clean
speech_command/clean: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/clean
speech_command/clean: speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/clean
speech_command/clean: speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/clean
speech_command/clean: speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/clean
speech_command/clean: speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/clean

.PHONY : speech_command/clean

# Convenience name for "preinstall" pass in the directory.
speech_command/preinstall:

.PHONY : speech_command/preinstall

#=============================================================================
# Target rules for target speech_command/CMakeFiles/audio_player.dir

# All Build rule for target.
speech_command/CMakeFiles/audio_player.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/audio_player.dir/build.make speech_command/CMakeFiles/audio_player.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/audio_player.dir/build.make speech_command/CMakeFiles/audio_player.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=6,7 "Built target audio_player"
.PHONY : speech_command/CMakeFiles/audio_player.dir/all

# Include target in all.
all: speech_command/CMakeFiles/audio_player.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/audio_player.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/audio_player.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/audio_player.dir/rule

# Convenience name for target.
audio_player: speech_command/CMakeFiles/audio_player.dir/rule

.PHONY : audio_player

# clean rule for target.
speech_command/CMakeFiles/audio_player.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/audio_player.dir/build.make speech_command/CMakeFiles/audio_player.dir/clean
.PHONY : speech_command/CMakeFiles/audio_player.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/audio_player.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/simple_speech_command_node.dir

# All Build rule for target.
speech_command/CMakeFiles/simple_speech_command_node.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/simple_speech_command_node.dir/build.make speech_command/CMakeFiles/simple_speech_command_node.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/simple_speech_command_node.dir/build.make speech_command/CMakeFiles/simple_speech_command_node.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=18,19 "Built target simple_speech_command_node"
.PHONY : speech_command/CMakeFiles/simple_speech_command_node.dir/all

# Include target in all.
all: speech_command/CMakeFiles/simple_speech_command_node.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/simple_speech_command_node.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/simple_speech_command_node.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/simple_speech_command_node.dir/rule

# Convenience name for target.
simple_speech_command_node: speech_command/CMakeFiles/simple_speech_command_node.dir/rule

.PHONY : simple_speech_command_node

# clean rule for target.
speech_command/CMakeFiles/simple_speech_command_node.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/simple_speech_command_node.dir/build.make speech_command/CMakeFiles/simple_speech_command_node.dir/clean
.PHONY : speech_command/CMakeFiles/simple_speech_command_node.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/simple_speech_command_node.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/roscpp_generate_messages_py.dir

# All Build rule for target.
speech_command/CMakeFiles/roscpp_generate_messages_py.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_py.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_py.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_py.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_py.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target roscpp_generate_messages_py"
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_py.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/roscpp_generate_messages_py.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/roscpp_generate_messages_py.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_py.dir/rule

# Convenience name for target.
roscpp_generate_messages_py: speech_command/CMakeFiles/roscpp_generate_messages_py.dir/rule

.PHONY : roscpp_generate_messages_py

# clean rule for target.
speech_command/CMakeFiles/roscpp_generate_messages_py.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_py.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_py.dir/clean
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_py.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/roscpp_generate_messages_py.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/roscpp_generate_messages_eus.dir

# All Build rule for target.
speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target roscpp_generate_messages_eus"
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/rule

# Convenience name for target.
roscpp_generate_messages_eus: speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/rule

.PHONY : roscpp_generate_messages_eus

# clean rule for target.
speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/clean
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir

# All Build rule for target.
speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target roscpp_generate_messages_lisp"
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/rule

# Convenience name for target.
roscpp_generate_messages_lisp: speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/rule

.PHONY : roscpp_generate_messages_lisp

# clean rule for target.
speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/clean
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir

# All Build rule for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target rosgraph_msgs_generate_messages_cpp"
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/rule

# Convenience name for target.
rosgraph_msgs_generate_messages_cpp: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/rule

.PHONY : rosgraph_msgs_generate_messages_cpp

# clean rule for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/clean
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/audio_recorder.dir

# All Build rule for target.
speech_command/CMakeFiles/audio_recorder.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/audio_recorder.dir/build.make speech_command/CMakeFiles/audio_recorder.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/audio_recorder.dir/build.make speech_command/CMakeFiles/audio_recorder.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=8,9 "Built target audio_recorder"
.PHONY : speech_command/CMakeFiles/audio_recorder.dir/all

# Include target in all.
all: speech_command/CMakeFiles/audio_recorder.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/audio_recorder.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/audio_recorder.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/audio_recorder.dir/rule

# Convenience name for target.
audio_recorder: speech_command/CMakeFiles/audio_recorder.dir/rule

.PHONY : audio_recorder

# clean rule for target.
speech_command/CMakeFiles/audio_recorder.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/audio_recorder.dir/build.make speech_command/CMakeFiles/audio_recorder.dir/clean
.PHONY : speech_command/CMakeFiles/audio_recorder.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/audio_recorder.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir

# All Build rule for target.
speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target std_msgs_generate_messages_lisp"
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/rule

# Convenience name for target.
std_msgs_generate_messages_lisp: speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/rule

.PHONY : std_msgs_generate_messages_lisp

# clean rule for target.
speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/clean
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir

# All Build rule for target.
speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target roscpp_generate_messages_cpp"
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/rule

# Convenience name for target.
roscpp_generate_messages_cpp: speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/rule

.PHONY : roscpp_generate_messages_cpp

# clean rule for target.
speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/clean
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir

# All Build rule for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target rosgraph_msgs_generate_messages_eus"
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/rule

# Convenience name for target.
rosgraph_msgs_generate_messages_eus: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/rule

.PHONY : rosgraph_msgs_generate_messages_eus

# clean rule for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/clean
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir

# All Build rule for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target rosgraph_msgs_generate_messages_lisp"
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/rule

# Convenience name for target.
rosgraph_msgs_generate_messages_lisp: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/rule

.PHONY : rosgraph_msgs_generate_messages_lisp

# clean rule for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/clean
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/speech_command_node.dir

# All Build rule for target.
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/audio_player.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/roscpp_generate_messages_py.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/audio_recorder.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/AIUITester.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/all
speech_command/CMakeFiles/speech_command_node.dir/all: speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/all
	$(MAKE) -f speech_command/CMakeFiles/speech_command_node.dir/build.make speech_command/CMakeFiles/speech_command_node.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/speech_command_node.dir/build.make speech_command/CMakeFiles/speech_command_node.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=20,21 "Built target speech_command_node"
.PHONY : speech_command/CMakeFiles/speech_command_node.dir/all

# Include target in all.
all: speech_command/CMakeFiles/speech_command_node.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/speech_command_node.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 11
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/speech_command_node.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/speech_command_node.dir/rule

# Convenience name for target.
speech_command_node: speech_command/CMakeFiles/speech_command_node.dir/rule

.PHONY : speech_command_node

# clean rule for target.
speech_command/CMakeFiles/speech_command_node.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/speech_command_node.dir/build.make speech_command/CMakeFiles/speech_command_node.dir/clean
.PHONY : speech_command/CMakeFiles/speech_command_node.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/speech_command_node.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/AIUITester.dir

# All Build rule for target.
speech_command/CMakeFiles/AIUITester.dir/all: speech_command/CMakeFiles/audio_player.dir/all
speech_command/CMakeFiles/AIUITester.dir/all: speech_command/CMakeFiles/audio_recorder.dir/all
	$(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=1,2,3,4,5 "Built target AIUITester"
.PHONY : speech_command/CMakeFiles/AIUITester.dir/all

# Include target in all.
all: speech_command/CMakeFiles/AIUITester.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/AIUITester.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 9
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/AIUITester.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/AIUITester.dir/rule

# Convenience name for target.
AIUITester: speech_command/CMakeFiles/AIUITester.dir/rule

.PHONY : AIUITester

# clean rule for target.
speech_command/CMakeFiles/AIUITester.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/clean
.PHONY : speech_command/CMakeFiles/AIUITester.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/AIUITester.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir

# All Build rule for target.
speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target std_msgs_generate_messages_cpp"
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/rule

# Convenience name for target.
std_msgs_generate_messages_cpp: speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/rule

.PHONY : std_msgs_generate_messages_cpp

# clean rule for target.
speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/clean
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir

# All Build rule for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target rosgraph_msgs_generate_messages_nodejs"
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/rule

# Convenience name for target.
rosgraph_msgs_generate_messages_nodejs: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/rule

.PHONY : rosgraph_msgs_generate_messages_nodejs

# clean rule for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/clean
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir

# All Build rule for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target rosgraph_msgs_generate_messages_py"
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/rule

# Convenience name for target.
rosgraph_msgs_generate_messages_py: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/rule

.PHONY : rosgraph_msgs_generate_messages_py

# clean rule for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/clean
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir

# All Build rule for target.
speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target std_msgs_generate_messages_eus"
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/rule

# Convenience name for target.
std_msgs_generate_messages_eus: speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/rule

.PHONY : std_msgs_generate_messages_eus

# clean rule for target.
speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/clean
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir

# All Build rule for target.
speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target std_msgs_generate_messages_nodejs"
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/rule

# Convenience name for target.
std_msgs_generate_messages_nodejs: speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/rule

.PHONY : std_msgs_generate_messages_nodejs

# clean rule for target.
speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/clean
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir

# All Build rule for target.
speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target roscpp_generate_messages_nodejs"
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/rule

# Convenience name for target.
roscpp_generate_messages_nodejs: speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/rule

.PHONY : roscpp_generate_messages_nodejs

# clean rule for target.
speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/clean
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target speech_command/CMakeFiles/std_msgs_generate_messages_py.dir

# All Build rule for target.
speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/all:
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/depend
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num= "Built target std_msgs_generate_messages_py"
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/all

# Build rule for subdir invocation for target.
speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/rule

# Convenience name for target.
std_msgs_generate_messages_py: speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/rule

.PHONY : std_msgs_generate_messages_py

# clean rule for target.
speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/clean:
	$(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/clean
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/clean

# clean rule for target.
clean: speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/clean

.PHONY : clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

