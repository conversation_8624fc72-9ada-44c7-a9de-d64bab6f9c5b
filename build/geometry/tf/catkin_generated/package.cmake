set(_CATKIN_CURRENT_PACKAGE "tf")
set(tf_VERSION "1.12.0")
set(tf_MAINTAINER "<PERSON><PERSON> <<EMAIL>>")
set(tf_PACKAGE_FORMAT "1")
set(tf_BUILD_DEPENDS "angles" "geometry_msgs" "message_filters" "message_generation" "rosconsole" "roscpp" "rostime" "sensor_msgs" "std_msgs" "tf2_ros")
set(tf_BUILD_DEPENDS_tf2_ros_VERSION_GTE "0.5.16")
set(tf_BUILD_EXPORT_DEPENDS "geometry_msgs" "graphviz" "message_filters" "message_runtime" "rosconsole" "roscpp" "roswtf" "sensor_msgs" "std_msgs" "tf2_ros")
set(tf_BUILD_EXPORT_DEPENDS_message_filters_VERSION_GTE "1.11.1")
set(tf_BUILD_EXPORT_DEPENDS_tf2_ros_VERSION_GTE "0.5.16")
set(tf_BUILDTOOL_DEPENDS "catkin")
set(tf_BUILDTOOL_DEPENDS_catkin_VERSION_GTE "0.6.4")
set(tf_BUILDTOOL_EXPORT_DEPENDS )
set(tf_EXEC_DEPENDS "geometry_msgs" "graphviz" "message_filters" "message_runtime" "rosconsole" "roscpp" "roswtf" "sensor_msgs" "std_msgs" "tf2_ros")
set(tf_EXEC_DEPENDS_message_filters_VERSION_GTE "1.11.1")
set(tf_EXEC_DEPENDS_tf2_ros_VERSION_GTE "0.5.16")
set(tf_RUN_DEPENDS "geometry_msgs" "graphviz" "message_filters" "message_runtime" "rosconsole" "roscpp" "roswtf" "sensor_msgs" "std_msgs" "tf2_ros")
set(tf_RUN_DEPENDS_message_filters_VERSION_GTE "1.11.1")
set(tf_RUN_DEPENDS_tf2_ros_VERSION_GTE "0.5.16")
set(tf_TEST_DEPENDS "rostest" "rosunit")
set(tf_DOC_DEPENDS )
set(tf_URL_WEBSITE "http://www.ros.org/wiki/tf")
set(tf_URL_BUGTRACKER "")
set(tf_URL_REPOSITORY "")
set(tf_DEPRECATED "")