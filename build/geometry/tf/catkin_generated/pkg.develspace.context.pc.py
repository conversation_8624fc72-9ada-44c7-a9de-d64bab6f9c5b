# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/ucar_ws/devel/include;/home/<USER>/ucar_ws/src/geometry/tf/include".split(';') if "/home/<USER>/ucar_ws/devel/include;/home/<USER>/ucar_ws/src/geometry/tf/include" != "" else []
PROJECT_CATKIN_DEPENDS = "geometry_msgs;message_filters;message_runtime;roscpp;sensor_msgs;std_msgs;tf2_ros;rosconsole".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-ltf".split(';') if "-ltf" != "" else []
PROJECT_NAME = "tf"
PROJECT_SPACE_DIR = "/home/<USER>/ucar_ws/devel"
PROJECT_VERSION = "1.12.0"
