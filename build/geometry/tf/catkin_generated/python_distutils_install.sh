#!/bin/sh

if [ -n "$DESTDIR" ] ; then
    case $DESTDIR in
        /*) # ok
            ;;
        *)
            /bin/echo "DESTDIR argument must be absolute... "
            /bin/echo "otherwise python's distutils will bork things."
            exit 1
    esac
fi

echo_and_run() { echo "+ $@" ; "$@" ; }

echo_and_run cd "/home/<USER>/ucar_ws/src/geometry/tf"

# ensure that Python install destination exists
echo_and_run mkdir -p "$DESTDIR/home/<USER>/ucar_ws/install/lib/python3/dist-packages"

# Note that PYTHONPATH is pulled from the environment to support installing
# into one location when some dependencies were installed in another
# location, #123.
echo_and_run /usr/bin/env \
    PYTHONPATH="/home/<USER>/ucar_ws/install/lib/python3/dist-packages:/home/<USER>/ucar_ws/build/lib/python3/dist-packages:$PYTHONPATH" \
    CATKIN_BINARY_DIR="/home/<USER>/ucar_ws/build" \
    "/usr/bin/python3" \
    "/home/<USER>/ucar_ws/src/geometry/tf/setup.py" \
     \
    build --build-base "/home/<USER>/ucar_ws/build/geometry/tf" \
    install \
    --root="${DESTDIR-/}" \
    --install-layout=deb --prefix="/home/<USER>/ucar_ws/install" --install-scripts="/home/<USER>/ucar_ws/install/bin"
