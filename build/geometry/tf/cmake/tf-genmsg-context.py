# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/ucar_ws/src/geometry/tf/msg/tfMessage.msg"
services_str = "/home/<USER>/ucar_ws/src/geometry/tf/srv/FrameGraph.srv"
pkg_name = "tf"
dependencies_str = "geometry_msgs;sensor_msgs;std_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "tf;/home/<USER>/ucar_ws/src/geometry/tf/msg;geometry_msgs;/opt/ros/noetic/share/geometry_msgs/cmake/../msg;sensor_msgs;/opt/ros/noetic/share/sensor_msgs/cmake/../msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python3"
package_has_static_sources = 'TRUE' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
