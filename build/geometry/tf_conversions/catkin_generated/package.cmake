set(_CATKIN_CURRENT_PACKAGE "tf_conversions")
set(tf_conversions_VERSION "1.12.0")
set(tf_conversions_MAINTAINER "Tu<PERSON> <<EMAIL>>")
set(tf_conversions_PACKAGE_FORMAT "1")
set(tf_conversions_BUILD_DEPENDS "eigen" "geometry_msgs" "kdl_conversions" "orocos_kdl" "tf")
set(tf_conversions_BUILD_EXPORT_DEPENDS "eigen" "geometry_msgs" "kdl_conversions" "orocos_kdl" "python_orocos_kdl" "tf")
set(tf_conversions_BUILDTOOL_DEPENDS "catkin")
set(tf_conversions_BUILDTOOL_DEPENDS_catkin_VERSION_GTE "0.5.68")
set(tf_conversions_BUILDTOOL_EXPORT_DEPENDS )
set(tf_conversions_EXEC_DEPENDS "eigen" "geometry_msgs" "kdl_conversions" "orocos_kdl" "python_orocos_kdl" "tf")
set(tf_conversions_RUN_DEPENDS "eigen" "geometry_msgs" "kdl_conversions" "orocos_kdl" "python_orocos_kdl" "tf")
set(tf_conversions_TEST_DEPENDS )
set(tf_conversions_DOC_DEPENDS )
set(tf_conversions_URL_WEBSITE "http://www.ros.org/wiki/tf_conversions")
set(tf_conversions_URL_BUGTRACKER "")
set(tf_conversions_URL_REPOSITORY "")
set(tf_conversions_DEPRECATED "")