<package>
  <name>geometry2</name>
  <version>0.6.5</version>
  <description>
    A metapackage to bring in the default packages second generation Transform Library in ros, tf2.
  </description>
  <author><PERSON><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <license>BSD</license>

  <url type="website">http://www.ros.org/wiki/geometry2</url>
    
  <buildtool_depend>catkin</buildtool_depend>

  <run_depend>tf2</run_depend>
  <run_depend>tf2_bullet</run_depend>
  <run_depend>tf2_eigen</run_depend>
  <run_depend>tf2_geometry_msgs</run_depend>
  <run_depend>tf2_kdl</run_depend>
  <run_depend>tf2_msgs</run_depend>
  <run_depend>tf2_py</run_depend>
  <run_depend>tf2_ros</run_depend>
  <run_depend>tf2_sensor_msgs</run_depend>
  <run_depend>tf2_tools</run_depend>

  <export>
    <metapackage/>
  </export>
</package>
