set(_CATKIN_CURRENT_PACKAGE "geometry2")
set(geometry2_VERSION "0.6.5")
set(geometry2_MAINTAINER "<PERSON><PERSON> <<EMAIL>>")
set(geometry2_PACKAGE_FORMAT "1")
set(geometry2_BUILD_DEPENDS )
set(geometry2_BUILD_EXPORT_DEPENDS "tf2" "tf2_bullet" "tf2_eigen" "tf2_geometry_msgs" "tf2_kdl" "tf2_msgs" "tf2_py" "tf2_ros" "tf2_sensor_msgs" "tf2_tools")
set(geometry2_BUILDTOOL_DEPENDS "catkin")
set(geometry2_BUILDTOOL_EXPORT_DEPENDS )
set(geometry2_EXEC_DEPENDS "tf2" "tf2_bullet" "tf2_eigen" "tf2_geometry_msgs" "tf2_kdl" "tf2_msgs" "tf2_py" "tf2_ros" "tf2_sensor_msgs" "tf2_tools")
set(geometry2_RUN_DEPENDS "tf2" "tf2_bullet" "tf2_eigen" "tf2_geometry_msgs" "tf2_kdl" "tf2_msgs" "tf2_py" "tf2_ros" "tf2_sensor_msgs" "tf2_tools")
set(geometry2_TEST_DEPENDS )
set(geometry2_DOC_DEPENDS )
set(geometry2_URL_WEBSITE "http://www.ros.org/wiki/geometry2")
set(geometry2_URL_BUGTRACKER "")
set(geometry2_URL_REPOSITORY "")
set(geometry2_DEPRECATED "")