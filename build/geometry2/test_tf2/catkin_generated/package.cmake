set(_CATKIN_CURRENT_PACKAGE "test_tf2")
set(test_tf2_VERSION "0.6.5")
set(test_tf2_MAINTAINER "Tu<PERSON> <<EMAIL>>")
set(test_tf2_PACKAGE_FORMAT "1")
set(test_tf2_BUILD_DEPENDS "rosconsole" "roscpp" "rostest" "tf" "tf2" "tf2_bullet" "tf2_ros" "tf2_geometry_msgs" "tf2_kdl" "tf2_msgs")
set(test_tf2_BUILD_EXPORT_DEPENDS "rosconsole" "roscpp" "rostest" "tf" "tf2" "tf2_bullet" "tf2_ros" "tf2_geometry_msgs" "tf2_kdl" "tf2_msgs")
set(test_tf2_BUILDTOOL_DEPENDS "catkin")
set(test_tf2_BUILDTOOL_DEPENDS_catkin_VERSION_GTE "0.5.68")
set(test_tf2_BUILDTOOL_EXPORT_DEPENDS )
set(test_tf2_EXEC_DEPENDS "rosconsole" "roscpp" "rostest" "tf" "tf2" "tf2_bullet" "tf2_ros" "tf2_geometry_msgs" "tf2_kdl" "tf2_msgs")
set(test_tf2_RUN_DEPENDS "rosconsole" "roscpp" "rostest" "tf" "tf2" "tf2_bullet" "tf2_ros" "tf2_geometry_msgs" "tf2_kdl" "tf2_msgs")
set(test_tf2_TEST_DEPENDS "rosunit" "rosbash")
set(test_tf2_DOC_DEPENDS )
set(test_tf2_URL_WEBSITE "http://www.ros.org/wiki/geometry_experimental")
set(test_tf2_URL_BUGTRACKER "")
set(test_tf2_URL_REPOSITORY "")
set(test_tf2_DEPRECATED "")