<package>
  <name>test_tf2</name>
  <version>0.6.5</version>
  <description>
    tf2 unit tests
  </description>
  <author><PERSON><PERSON></author>
  <author><PERSON><PERSON><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <license>BSD</license>

  <url type="website">http://www.ros.org/wiki/geometry_experimental</url>
    
  <buildtool_depend version_gte="0.5.68">catkin</buildtool_depend>

  <build_depend>rosconsole</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>rostest</build_depend>
  <build_depend>tf</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_bullet</build_depend>
  <build_depend>tf2_ros</build_depend>
  <build_depend>tf2_geometry_msgs</build_depend>
  <build_depend>tf2_kdl</build_depend>
  <build_depend>tf2_msgs</build_depend>

  <run_depend>rosconsole</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>rostest</run_depend>
  <run_depend>tf</run_depend>
  <run_depend>tf2</run_depend>
  <run_depend>tf2_bullet</run_depend>
  <run_depend>tf2_ros</run_depend>
  <run_depend>tf2_geometry_msgs</run_depend>
  <run_depend>tf2_kdl</run_depend>
  <run_depend>tf2_msgs</run_depend>

  <test_depend>rosunit</test_depend>
  <test_depend>rosbash</test_depend>

</package>


