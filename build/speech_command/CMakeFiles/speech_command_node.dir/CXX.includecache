#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/ucar_ws/src/speech_command/include/AIUITester.h
aiui/AIUI.h
-
FileUtil.h
-
WriteAudioThread.h
-
AudioRecorder.h
-
TestListener.h
-
AudioPlayer.h
-
msp_cmn.h
-
iostream
-
Global.h
-
jsoncpp/json/json.h
/home/<USER>/ucar_ws/src/speech_command/include/jsoncpp/json/json.h
iostream
-
RingBuffer.h
-
qisr.h
/home/<USER>/ucar_ws/src/speech_command/include/qisr.h
msp_cmn.h
/home/<USER>/ucar_ws/src/speech_command/include/msp_cmn.h
msp_errors.h
/home/<USER>/ucar_ws/src/speech_command/include/msp_errors.h
string
-
alsa/asoundlib.h
/home/<USER>/ucar_ws/src/speech_command/include/alsa/asoundlib.h
stdio.h
/home/<USER>/ucar_ws/src/speech_command/include/stdio.h
stdlib.h
/home/<USER>/ucar_ws/src/speech_command/include/stdlib.h
numeric
-
signal.h
/home/<USER>/ucar_ws/src/speech_command/include/signal.h
ros/ros.h
-
std_srvs/Trigger.h
-

/home/<USER>/ucar_ws/src/speech_command/include/AudioPlayer.h
alsa/asoundlib.h
-

/home/<USER>/ucar_ws/src/speech_command/include/AudioRecorder.h
hidapi.h
/home/<USER>/ucar_ws/src/speech_command/include/hidapi.h
iostream
-

/home/<USER>/ucar_ws/src/speech_command/include/FileUtil.h
iostream
-
string
-
fstream
-
aiui/AIUIType.h
-
windows.h
-
unistd.h
-
pthread.h
-

/home/<USER>/ucar_ws/src/speech_command/include/Global.h
iostream
-
aiui/AIUI.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUI.h
FileUtil.h
/home/<USER>/ucar_ws/src/speech_command/include/FileUtil.h
AudioPlayer.h
-
map
-
serial/serial.h
-

/home/<USER>/ucar_ws/src/speech_command/include/RingBuffer.h

/home/<USER>/ucar_ws/src/speech_command/include/TestListener.h
aiui/AIUI.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUI.h
FileUtil.h
/home/<USER>/ucar_ws/src/speech_command/include/FileUtil.h

/home/<USER>/ucar_ws/src/speech_command/include/WriteAudioThread.h
aiui/AIUI.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUI.h
iostream
-
FileUtil.h
/home/<USER>/ucar_ws/src/speech_command/include/FileUtil.h
stdio.h
-
stdlib.h
-
string.h
-

/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUI.h
AIUIConstant.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUIConstant.h
AIUIErrorCode.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUIErrorCode.h
AIUIType.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUIType.h
AIUICommon.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUICommon.h
aiui_internal/AIUI_internal.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/aiui_internal/AIUI_internal.h

/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUICommon.h

/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUIConstant.h
AIUICommon.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUICommon.h

/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUIErrorCode.h
AIUICommon.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUICommon.h

/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUIType.h
AIUICommon.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/AIUICommon.h
stdint.h
-
sys/types.h
-
aiui_internal/AIUIType_internal.h
/home/<USER>/ucar_ws/src/speech_command/include/aiui/aiui_internal/AIUIType_internal.h

/home/<USER>/ucar_ws/src/speech_command/include/hidapi.h
wchar.h
-
protocol_proc_unit.h
/home/<USER>/ucar_ws/src/speech_command/include/protocol_proc_unit.h

/home/<USER>/ucar_ws/src/speech_command/include/msp_cmn.h
msp_types.h
/home/<USER>/ucar_ws/src/speech_command/include/msp_types.h

/home/<USER>/ucar_ws/src/speech_command/include/msp_errors.h

/home/<USER>/ucar_ws/src/speech_command/include/msp_types.h

/home/<USER>/ucar_ws/src/speech_command/include/protocol_proc_unit.h
pthread.h
-
queue_simple.h
/home/<USER>/ucar_ws/src/speech_command/include/queue_simple.h

/home/<USER>/ucar_ws/src/speech_command/include/qisr.h
msp_types.h
/home/<USER>/ucar_ws/src/speech_command/include/msp_types.h

/home/<USER>/ucar_ws/src/speech_command/include/queue_simple.h
stdio.h
-
stdlib.h
-
stdint.h
-
unistd.h
-
windows.h
-
errno.h
-
pthread.h
-

/home/<USER>/ucar_ws/src/speech_command/src/aiuiMain.cpp
iostream
-
ros/ros.h
-
ros/package.h
-
AIUITester.h
-
signal.h
-
std_msgs/String.h
/home/<USER>/ucar_ws/src/speech_command/src/std_msgs/String.h
std_msgs/Int32.h
/home/<USER>/ucar_ws/src/speech_command/src/std_msgs/Int32.h
boost/algorithm/string/classification.hpp
-
boost/algorithm/string/split.hpp
-
thread
-
iostream
-
mutex
-
jsoncpp/json/json.h
/home/<USER>/ucar_ws/src/speech_command/src/jsoncpp/json/json.h

/opt/ros/noetic/include/ros/advertise_options.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/message_traits.h
/opt/ros/noetic/include/ros/ros/message_traits.h
common.h
/opt/ros/noetic/include/ros/common.h

/opt/ros/noetic/include/ros/advertise_service_options.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/service_callback_helper.h
/opt/ros/noetic/include/ros/ros/service_callback_helper.h
ros/service_traits.h
/opt/ros/noetic/include/ros/ros/service_traits.h
ros/message_traits.h
/opt/ros/noetic/include/ros/ros/message_traits.h
common.h
/opt/ros/noetic/include/ros/common.h

/opt/ros/noetic/include/ros/assert.h
ros/console.h
/opt/ros/noetic/include/ros/ros/console.h
ros/static_assert.h
/opt/ros/noetic/include/ros/ros/static_assert.h
ros/platform.h
-
stdlib.h
-

/opt/ros/noetic/include/ros/builtin_message_traits.h
message_traits.h
/opt/ros/noetic/include/ros/message_traits.h
ros/time.h
/opt/ros/noetic/include/ros/ros/time.h

/opt/ros/noetic/include/ros/common.h
stdint.h
-
assert.h
-
stddef.h
-
string
-
ros/assert.h
/opt/ros/noetic/include/ros/ros/assert.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/serialized_message.h
/opt/ros/noetic/include/ros/ros/serialized_message.h
boost/shared_array.hpp
-
ros/macros.h
-

/opt/ros/noetic/include/ros/console.h
console_backend.h
/opt/ros/noetic/include/ros/console_backend.h
cstdio
-
sstream
-
ros/time.h
-
cstdarg
-
ros/macros.h
-
map
-
vector
-
log4cxx/level.h
/opt/ros/noetic/include/ros/log4cxx/level.h
rosconsole/macros_generated.h
/opt/ros/noetic/include/ros/rosconsole/macros_generated.h

/opt/ros/noetic/include/ros/console_backend.h
ros/macros.h
-

/opt/ros/noetic/include/ros/datatypes.h
string
-
vector
-
map
-
set
-
list
-
boost/shared_ptr.hpp
-

/opt/ros/noetic/include/ros/duration.h
iostream
-
math.h
-
stdexcept
-
climits
-
stdint.h
-
rostime_decl.h
/opt/ros/noetic/include/ros/rostime_decl.h

/opt/ros/noetic/include/ros/exception.h
stdexcept
-

/opt/ros/noetic/include/ros/exceptions.h
ros/exception.h
-

/opt/ros/noetic/include/ros/forwards.h
string
-
vector
-
map
-
set
-
list
-
boost/shared_ptr.hpp
-
boost/make_shared.hpp
-
boost/weak_ptr.hpp
-
boost/function.hpp
-
ros/time.h
-
ros/macros.h
-
exceptions.h
/opt/ros/noetic/include/ros/exceptions.h
ros/datatypes.h
/opt/ros/noetic/include/ros/ros/datatypes.h

/opt/ros/noetic/include/ros/init.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/spinner.h
/opt/ros/noetic/include/ros/ros/spinner.h
common.h
/opt/ros/noetic/include/ros/common.h

/opt/ros/noetic/include/ros/macros.h

/opt/ros/noetic/include/ros/master.h
forwards.h
/opt/ros/noetic/include/ros/forwards.h
xmlrpcpp/XmlRpcValue.h
/opt/ros/noetic/include/ros/xmlrpcpp/XmlRpcValue.h
common.h
/opt/ros/noetic/include/ros/common.h

/opt/ros/noetic/include/ros/message.h
ros/macros.h
/opt/ros/noetic/include/ros/ros/macros.h
ros/assert.h
/opt/ros/noetic/include/ros/ros/assert.h
string
-
string.h
-
boost/shared_ptr.hpp
-
boost/array.hpp
-
stdint.h
-

/opt/ros/noetic/include/ros/message_event.h
ros/time.h
/opt/ros/noetic/include/ros/ros/time.h
ros/datatypes.h
-
ros/message_traits.h
-
boost/type_traits/is_void.hpp
-
boost/type_traits/is_base_of.hpp
-
boost/type_traits/is_const.hpp
-
boost/type_traits/add_const.hpp
-
boost/type_traits/remove_const.hpp
-
boost/utility/enable_if.hpp
-
boost/function.hpp
-
boost/make_shared.hpp
-

/opt/ros/noetic/include/ros/message_forward.h
cstddef
-
memory
-

/opt/ros/noetic/include/ros/message_operations.h
ostream
-

/opt/ros/noetic/include/ros/message_traits.h
message_forward.h
/opt/ros/noetic/include/ros/message_forward.h
ros/time.h
-
string
-
boost/utility/enable_if.hpp
-
boost/type_traits/remove_const.hpp
-
boost/type_traits/remove_reference.hpp
-

/opt/ros/noetic/include/ros/names.h
forwards.h
/opt/ros/noetic/include/ros/forwards.h
common.h
/opt/ros/noetic/include/ros/common.h

/opt/ros/noetic/include/ros/node_handle.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/publisher.h
/opt/ros/noetic/include/ros/ros/publisher.h
ros/subscriber.h
/opt/ros/noetic/include/ros/ros/subscriber.h
ros/service_server.h
/opt/ros/noetic/include/ros/ros/service_server.h
ros/service_client.h
/opt/ros/noetic/include/ros/ros/service_client.h
ros/timer.h
/opt/ros/noetic/include/ros/ros/timer.h
ros/rate.h
/opt/ros/noetic/include/ros/ros/rate.h
ros/wall_timer.h
/opt/ros/noetic/include/ros/ros/wall_timer.h
ros/steady_timer.h
/opt/ros/noetic/include/ros/ros/steady_timer.h
ros/advertise_options.h
/opt/ros/noetic/include/ros/ros/advertise_options.h
ros/advertise_service_options.h
/opt/ros/noetic/include/ros/ros/advertise_service_options.h
ros/subscribe_options.h
/opt/ros/noetic/include/ros/ros/subscribe_options.h
ros/service_client_options.h
/opt/ros/noetic/include/ros/ros/service_client_options.h
ros/timer_options.h
/opt/ros/noetic/include/ros/ros/timer_options.h
ros/wall_timer_options.h
/opt/ros/noetic/include/ros/ros/wall_timer_options.h
ros/spinner.h
/opt/ros/noetic/include/ros/ros/spinner.h
ros/init.h
/opt/ros/noetic/include/ros/ros/init.h
common.h
/opt/ros/noetic/include/ros/common.h
boost/bind.hpp
-
xmlrpcpp/XmlRpcValue.h
-

/opt/ros/noetic/include/ros/package.h
string
-
utility
-
vector
-
map
-

/opt/ros/noetic/include/ros/param.h
forwards.h
/opt/ros/noetic/include/ros/forwards.h
common.h
/opt/ros/noetic/include/ros/common.h
xmlrpcpp/XmlRpcValue.h
/opt/ros/noetic/include/ros/xmlrpcpp/XmlRpcValue.h
vector
-
map
-

/opt/ros/noetic/include/ros/parameter_adapter.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/message_event.h
/opt/ros/noetic/include/ros/ros/message_event.h
ros/static_assert.h
-
boost/type_traits/add_const.hpp
-
boost/type_traits/remove_const.hpp
-
boost/type_traits/remove_reference.hpp
-

/opt/ros/noetic/include/ros/platform.h
stdlib.h
-
string
-

/opt/ros/noetic/include/ros/publisher.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/common.h
/opt/ros/noetic/include/ros/ros/common.h
ros/message.h
/opt/ros/noetic/include/ros/ros/message.h
ros/serialization.h
/opt/ros/noetic/include/ros/ros/serialization.h
boost/bind.hpp
-
boost/thread/mutex.hpp
-

/opt/ros/noetic/include/ros/rate.h
ros/time.h
/opt/ros/noetic/include/ros/ros/time.h
rostime_decl.h
/opt/ros/noetic/include/ros/rostime_decl.h

/opt/ros/noetic/include/ros/ros.h
ros/time.h
/opt/ros/noetic/include/ros/ros/time.h
ros/rate.h
/opt/ros/noetic/include/ros/ros/rate.h
ros/console.h
/opt/ros/noetic/include/ros/ros/console.h
ros/assert.h
/opt/ros/noetic/include/ros/ros/assert.h
ros/common.h
/opt/ros/noetic/include/ros/ros/common.h
ros/types.h
/opt/ros/noetic/include/ros/ros/types.h
ros/node_handle.h
/opt/ros/noetic/include/ros/ros/node_handle.h
ros/publisher.h
/opt/ros/noetic/include/ros/ros/publisher.h
ros/single_subscriber_publisher.h
/opt/ros/noetic/include/ros/ros/single_subscriber_publisher.h
ros/service_server.h
/opt/ros/noetic/include/ros/ros/service_server.h
ros/subscriber.h
/opt/ros/noetic/include/ros/ros/subscriber.h
ros/service.h
/opt/ros/noetic/include/ros/ros/service.h
ros/init.h
/opt/ros/noetic/include/ros/ros/init.h
ros/master.h
/opt/ros/noetic/include/ros/ros/master.h
ros/this_node.h
/opt/ros/noetic/include/ros/ros/this_node.h
ros/param.h
/opt/ros/noetic/include/ros/ros/param.h
ros/topic.h
/opt/ros/noetic/include/ros/ros/topic.h
ros/names.h
/opt/ros/noetic/include/ros/ros/names.h

/opt/ros/noetic/include/ros/roscpp_serialization_macros.h
ros/macros.h
-

/opt/ros/noetic/include/ros/rostime_decl.h
ros/macros.h
-

/opt/ros/noetic/include/ros/serialization.h
roscpp_serialization_macros.h
/opt/ros/noetic/include/ros/roscpp_serialization_macros.h
ros/types.h
-
ros/time.h
-
serialized_message.h
/opt/ros/noetic/include/ros/serialized_message.h
ros/message_traits.h
/opt/ros/noetic/include/ros/ros/message_traits.h
ros/builtin_message_traits.h
/opt/ros/noetic/include/ros/ros/builtin_message_traits.h
ros/exception.h
/opt/ros/noetic/include/ros/ros/exception.h
ros/datatypes.h
/opt/ros/noetic/include/ros/ros/datatypes.h
vector
-
map
-
boost/array.hpp
-
boost/call_traits.hpp
-
boost/utility/enable_if.hpp
-
boost/mpl/and.hpp
-
boost/mpl/or.hpp
-
boost/mpl/not.hpp
-
cstring
-

/opt/ros/noetic/include/ros/serialized_message.h
roscpp_serialization_macros.h
/opt/ros/noetic/include/ros/roscpp_serialization_macros.h
boost/shared_array.hpp
-
boost/shared_ptr.hpp
-

/opt/ros/noetic/include/ros/service.h
string
-
ros/common.h
/opt/ros/noetic/include/ros/ros/common.h
ros/message.h
/opt/ros/noetic/include/ros/ros/message.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/node_handle.h
/opt/ros/noetic/include/ros/ros/node_handle.h
ros/service_traits.h
/opt/ros/noetic/include/ros/ros/service_traits.h
ros/names.h
/opt/ros/noetic/include/ros/ros/names.h
boost/shared_ptr.hpp
-

/opt/ros/noetic/include/ros/service_callback_helper.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/common.h
/opt/ros/noetic/include/ros/ros/common.h
ros/message.h
/opt/ros/noetic/include/ros/ros/message.h
ros/message_traits.h
/opt/ros/noetic/include/ros/ros/message_traits.h
ros/service_traits.h
/opt/ros/noetic/include/ros/ros/service_traits.h
ros/serialization.h
/opt/ros/noetic/include/ros/ros/serialization.h
boost/type_traits/is_base_of.hpp
-
boost/utility/enable_if.hpp
-

/opt/ros/noetic/include/ros/service_client.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/common.h
/opt/ros/noetic/include/ros/ros/common.h
ros/service_traits.h
/opt/ros/noetic/include/ros/ros/service_traits.h
ros/serialization.h
/opt/ros/noetic/include/ros/ros/serialization.h

/opt/ros/noetic/include/ros/service_client_options.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
common.h
/opt/ros/noetic/include/ros/common.h
ros/service_traits.h
/opt/ros/noetic/include/ros/ros/service_traits.h

/opt/ros/noetic/include/ros/service_server.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
common.h
/opt/ros/noetic/include/ros/common.h

/opt/ros/noetic/include/ros/service_traits.h
boost/type_traits/remove_reference.hpp
-
boost/type_traits/remove_const.hpp
-

/opt/ros/noetic/include/ros/single_subscriber_publisher.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/serialization.h
/opt/ros/noetic/include/ros/ros/serialization.h
common.h
/opt/ros/noetic/include/ros/common.h
boost/utility.hpp
-

/opt/ros/noetic/include/ros/spinner.h
ros/types.h
/opt/ros/noetic/include/ros/ros/types.h
common.h
/opt/ros/noetic/include/ros/common.h
boost/shared_ptr.hpp
-

/opt/ros/noetic/include/ros/static_assert.h
boost/static_assert.hpp
-

/opt/ros/noetic/include/ros/steady_timer.h
common.h
/opt/ros/noetic/include/ros/common.h
forwards.h
/opt/ros/noetic/include/ros/forwards.h
steady_timer_options.h
/opt/ros/noetic/include/ros/steady_timer_options.h

/opt/ros/noetic/include/ros/steady_timer_options.h
common.h
/opt/ros/noetic/include/ros/common.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h

/opt/ros/noetic/include/ros/subscribe_options.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
common.h
/opt/ros/noetic/include/ros/common.h
ros/transport_hints.h
/opt/ros/noetic/include/ros/ros/transport_hints.h
ros/message_traits.h
/opt/ros/noetic/include/ros/ros/message_traits.h
subscription_callback_helper.h
/opt/ros/noetic/include/ros/subscription_callback_helper.h

/opt/ros/noetic/include/ros/subscriber.h
common.h
/opt/ros/noetic/include/ros/common.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/subscription_callback_helper.h
/opt/ros/noetic/include/ros/ros/subscription_callback_helper.h

/opt/ros/noetic/include/ros/subscription_callback_helper.h
typeinfo
-
common.h
/opt/ros/noetic/include/ros/common.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
ros/parameter_adapter.h
/opt/ros/noetic/include/ros/ros/parameter_adapter.h
ros/message_traits.h
/opt/ros/noetic/include/ros/ros/message_traits.h
ros/builtin_message_traits.h
/opt/ros/noetic/include/ros/ros/builtin_message_traits.h
ros/serialization.h
/opt/ros/noetic/include/ros/ros/serialization.h
ros/message_event.h
/opt/ros/noetic/include/ros/ros/message_event.h
ros/static_assert.h
-
boost/type_traits/add_const.hpp
-
boost/type_traits/remove_const.hpp
-
boost/type_traits/remove_reference.hpp
-
boost/type_traits/is_base_of.hpp
-
boost/utility/enable_if.hpp
-
boost/make_shared.hpp
-

/opt/ros/noetic/include/ros/this_node.h
common.h
/opt/ros/noetic/include/ros/common.h
forwards.h
/opt/ros/noetic/include/ros/forwards.h

/opt/ros/noetic/include/ros/time.h
ros/platform.h
-
iostream
-
cmath
-
ros/exception.h
-
duration.h
/opt/ros/noetic/include/ros/duration.h
boost/math/special_functions/round.hpp
-
rostime_decl.h
/opt/ros/noetic/include/ros/rostime_decl.h
sys/timeb.h
-
sys/time.h
-

/opt/ros/noetic/include/ros/timer.h
common.h
/opt/ros/noetic/include/ros/common.h
forwards.h
/opt/ros/noetic/include/ros/forwards.h
timer_options.h
/opt/ros/noetic/include/ros/timer_options.h

/opt/ros/noetic/include/ros/timer_options.h
common.h
/opt/ros/noetic/include/ros/common.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h

/opt/ros/noetic/include/ros/topic.h
common.h
/opt/ros/noetic/include/ros/common.h
node_handle.h
/opt/ros/noetic/include/ros/node_handle.h
boost/shared_ptr.hpp
-

/opt/ros/noetic/include/ros/transport_hints.h
common.h
/opt/ros/noetic/include/ros/common.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h
boost/lexical_cast.hpp
-

/opt/ros/noetic/include/ros/types.h
stdint.h
-

/opt/ros/noetic/include/ros/wall_timer.h
common.h
/opt/ros/noetic/include/ros/common.h
forwards.h
/opt/ros/noetic/include/ros/forwards.h
wall_timer_options.h
/opt/ros/noetic/include/ros/wall_timer_options.h

/opt/ros/noetic/include/ros/wall_timer_options.h
common.h
/opt/ros/noetic/include/ros/common.h
ros/forwards.h
/opt/ros/noetic/include/ros/ros/forwards.h

/opt/ros/noetic/include/rosconsole/macros_generated.h

/opt/ros/noetic/include/serial/serial.h
limits
-
vector
-
string
-
cstring
-
sstream
-
exception
-
stdexcept
-
serial/v8stdint.h
-

/opt/ros/noetic/include/serial/v8stdint.h
stddef.h
-
stdio.h
-
stdint.h
-

/opt/ros/noetic/include/std_msgs/Int32.h
string
-
vector
-
memory
-
ros/types.h
-
ros/serialization.h
-
ros/builtin_message_traits.h
-
ros/message_operations.h
-

/opt/ros/noetic/include/std_msgs/String.h
string
-
vector
-
memory
-
ros/types.h
-
ros/serialization.h
-
ros/builtin_message_traits.h
-
ros/message_operations.h
-

/opt/ros/noetic/include/std_srvs/Trigger.h
ros/service_traits.h
-
std_srvs/TriggerRequest.h
-
std_srvs/TriggerResponse.h
-

/opt/ros/noetic/include/std_srvs/TriggerRequest.h
string
-
vector
-
memory
-
ros/types.h
-
ros/serialization.h
-
ros/builtin_message_traits.h
-
ros/message_operations.h
-

/opt/ros/noetic/include/std_srvs/TriggerResponse.h
string
-
vector
-
memory
-
ros/types.h
-
ros/serialization.h
-
ros/builtin_message_traits.h
-
ros/message_operations.h
-

/opt/ros/noetic/include/xmlrpcpp/XmlRpcDecl.h
ros/macros.h
-

/opt/ros/noetic/include/xmlrpcpp/XmlRpcValue.h
xmlrpcpp/XmlRpcDecl.h
/opt/ros/noetic/include/xmlrpcpp/xmlrpcpp/XmlRpcDecl.h
map
-
string
-
vector
-
time.h
-

