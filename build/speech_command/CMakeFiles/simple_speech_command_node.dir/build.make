# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ucar_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ucar_ws/build

# Include any dependencies generated for this target.
include speech_command/CMakeFiles/simple_speech_command_node.dir/depend.make

# Include the progress variables for this target.
include speech_command/CMakeFiles/simple_speech_command_node.dir/progress.make

# Include the compile flags for this target's objects.
include speech_command/CMakeFiles/simple_speech_command_node.dir/flags.make

speech_command/CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.o: speech_command/CMakeFiles/simple_speech_command_node.dir/flags.make
speech_command/CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.o: /home/<USER>/ucar_ws/src/speech_command/src/simple_speech_node.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object speech_command/CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.o"
	cd /home/<USER>/ucar_ws/build/speech_command && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.o -c /home/<USER>/ucar_ws/src/speech_command/src/simple_speech_node.cpp

speech_command/CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.i"
	cd /home/<USER>/ucar_ws/build/speech_command && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/ucar_ws/src/speech_command/src/simple_speech_node.cpp > CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.i

speech_command/CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.s"
	cd /home/<USER>/ucar_ws/build/speech_command && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/ucar_ws/src/speech_command/src/simple_speech_node.cpp -o CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.s

# Object files for target simple_speech_command_node
simple_speech_command_node_OBJECTS = \
"CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.o"

# External object files for target simple_speech_command_node
simple_speech_command_node_EXTERNAL_OBJECTS =

/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: speech_command/CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.o
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: speech_command/CMakeFiles/simple_speech_command_node.dir/build.make
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/libroscpp.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/librosconsole.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/librosconsole_log4cxx.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/librosconsole_backend_interface.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/liblog4cxx.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libboost_regex.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/libxmlrpcpp.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/libroscpp_serialization.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/librostime.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/libcpp_common.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libboost_thread.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libboost_chrono.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libboost_date_time.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libboost_atomic.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libconsole_bridge.so.0.4
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/libroslib.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/librospack.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libpython3.7m.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libboost_filesystem.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libboost_program_options.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libboost_system.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /usr/lib/aarch64-linux-gnu/libtinyxml2.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: /opt/ros/noetic/lib/libserial.so
/home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node: speech_command/CMakeFiles/simple_speech_command_node.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable /home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node"
	cd /home/<USER>/ucar_ws/build/speech_command && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/simple_speech_command_node.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
speech_command/CMakeFiles/simple_speech_command_node.dir/build: /home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node

.PHONY : speech_command/CMakeFiles/simple_speech_command_node.dir/build

speech_command/CMakeFiles/simple_speech_command_node.dir/clean:
	cd /home/<USER>/ucar_ws/build/speech_command && $(CMAKE_COMMAND) -P CMakeFiles/simple_speech_command_node.dir/cmake_clean.cmake
.PHONY : speech_command/CMakeFiles/simple_speech_command_node.dir/clean

speech_command/CMakeFiles/simple_speech_command_node.dir/depend:
	cd /home/<USER>/ucar_ws/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ucar_ws/src /home/<USER>/ucar_ws/src/speech_command /home/<USER>/ucar_ws/build /home/<USER>/ucar_ws/build/speech_command /home/<USER>/ucar_ws/build/speech_command/CMakeFiles/simple_speech_command_node.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : speech_command/CMakeFiles/simple_speech_command_node.dir/depend

