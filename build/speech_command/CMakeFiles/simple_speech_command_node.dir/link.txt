/usr/bin/c++    -rdynamic -pthread CMakeFiles/simple_speech_command_node.dir/src/simple_speech_node.cpp.o  -o /home/<USER>/ucar_ws/devel/lib/speech_command/simple_speech_command_node  -L/home/<USER>/ucar_ws/src/speech_command/lib/arm64  -L/usr/include -Wl,-rpath,/home/<USER>/ucar_ws/src/speech_command/lib/arm64:/usr/include:/opt/ros/noetic/lib /opt/ros/noetic/lib/libroscpp.so /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx -lboost_regex /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/librostime.so /opt/ros/noetic/lib/libcpp_common.so -lboost_thread -lpthread -lboost_chrono -lboost_date_time -lboost_atomic /usr/lib/aarch64-linux-gnu/libconsole_bridge.so.0.4 /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so -lpython3.7m -lboost_filesystem -lboost_program_options -lboost_system -ltinyxml2 /opt/ros/noetic/lib/libserial.so 
