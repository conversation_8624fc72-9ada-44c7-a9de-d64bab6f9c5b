# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ucar_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ucar_ws/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/ucar_ws/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles /home/<USER>/ucar_ws/build/speech_command/CMakeFiles/progress.marks
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/ucar_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
speech_command/CMakeFiles/audio_player.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/audio_player.dir/rule
.PHONY : speech_command/CMakeFiles/audio_player.dir/rule

# Convenience name for target.
audio_player: speech_command/CMakeFiles/audio_player.dir/rule

.PHONY : audio_player

# fast build rule for target.
audio_player/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/audio_player.dir/build.make speech_command/CMakeFiles/audio_player.dir/build
.PHONY : audio_player/fast

# Convenience name for target.
speech_command/CMakeFiles/roscpp_generate_messages_py.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/roscpp_generate_messages_py.dir/rule
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_py.dir/rule

# Convenience name for target.
roscpp_generate_messages_py: speech_command/CMakeFiles/roscpp_generate_messages_py.dir/rule

.PHONY : roscpp_generate_messages_py

# fast build rule for target.
roscpp_generate_messages_py/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_py.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_py.dir/build
.PHONY : roscpp_generate_messages_py/fast

# Convenience name for target.
speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/rule
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/rule

# Convenience name for target.
roscpp_generate_messages_eus: speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/rule

.PHONY : roscpp_generate_messages_eus

# fast build rule for target.
roscpp_generate_messages_eus/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_eus.dir/build
.PHONY : roscpp_generate_messages_eus/fast

# Convenience name for target.
speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/rule
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/rule

# Convenience name for target.
roscpp_generate_messages_lisp: speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/rule

.PHONY : roscpp_generate_messages_lisp

# fast build rule for target.
roscpp_generate_messages_lisp/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_lisp.dir/build
.PHONY : roscpp_generate_messages_lisp/fast

# Convenience name for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/rule
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/rule

# Convenience name for target.
rosgraph_msgs_generate_messages_cpp: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/rule

.PHONY : rosgraph_msgs_generate_messages_cpp

# fast build rule for target.
rosgraph_msgs_generate_messages_cpp/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_cpp.dir/build
.PHONY : rosgraph_msgs_generate_messages_cpp/fast

# Convenience name for target.
speech_command/CMakeFiles/audio_recorder.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/audio_recorder.dir/rule
.PHONY : speech_command/CMakeFiles/audio_recorder.dir/rule

# Convenience name for target.
audio_recorder: speech_command/CMakeFiles/audio_recorder.dir/rule

.PHONY : audio_recorder

# fast build rule for target.
audio_recorder/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/audio_recorder.dir/build.make speech_command/CMakeFiles/audio_recorder.dir/build
.PHONY : audio_recorder/fast

# Convenience name for target.
speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/rule
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/rule

# Convenience name for target.
std_msgs_generate_messages_lisp: speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/rule

.PHONY : std_msgs_generate_messages_lisp

# fast build rule for target.
std_msgs_generate_messages_lisp/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_lisp.dir/build
.PHONY : std_msgs_generate_messages_lisp/fast

# Convenience name for target.
speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/rule
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/rule

# Convenience name for target.
roscpp_generate_messages_cpp: speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/rule

.PHONY : roscpp_generate_messages_cpp

# fast build rule for target.
roscpp_generate_messages_cpp/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_cpp.dir/build
.PHONY : roscpp_generate_messages_cpp/fast

# Convenience name for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/rule
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/rule

# Convenience name for target.
rosgraph_msgs_generate_messages_eus: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/rule

.PHONY : rosgraph_msgs_generate_messages_eus

# fast build rule for target.
rosgraph_msgs_generate_messages_eus/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_eus.dir/build
.PHONY : rosgraph_msgs_generate_messages_eus/fast

# Convenience name for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/rule
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/rule

# Convenience name for target.
rosgraph_msgs_generate_messages_lisp: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/rule

.PHONY : rosgraph_msgs_generate_messages_lisp

# fast build rule for target.
rosgraph_msgs_generate_messages_lisp/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_lisp.dir/build
.PHONY : rosgraph_msgs_generate_messages_lisp/fast

# Convenience name for target.
speech_command/CMakeFiles/speech_command_node.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/speech_command_node.dir/rule
.PHONY : speech_command/CMakeFiles/speech_command_node.dir/rule

# Convenience name for target.
speech_command_node: speech_command/CMakeFiles/speech_command_node.dir/rule

.PHONY : speech_command_node

# fast build rule for target.
speech_command_node/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/speech_command_node.dir/build.make speech_command/CMakeFiles/speech_command_node.dir/build
.PHONY : speech_command_node/fast

# Convenience name for target.
speech_command/CMakeFiles/AIUITester.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/AIUITester.dir/rule
.PHONY : speech_command/CMakeFiles/AIUITester.dir/rule

# Convenience name for target.
AIUITester: speech_command/CMakeFiles/AIUITester.dir/rule

.PHONY : AIUITester

# fast build rule for target.
AIUITester/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/build
.PHONY : AIUITester/fast

# Convenience name for target.
speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/rule
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/rule

# Convenience name for target.
std_msgs_generate_messages_cpp: speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/rule

.PHONY : std_msgs_generate_messages_cpp

# fast build rule for target.
std_msgs_generate_messages_cpp/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_cpp.dir/build
.PHONY : std_msgs_generate_messages_cpp/fast

# Convenience name for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/rule
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/rule

# Convenience name for target.
rosgraph_msgs_generate_messages_nodejs: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/rule

.PHONY : rosgraph_msgs_generate_messages_nodejs

# fast build rule for target.
rosgraph_msgs_generate_messages_nodejs/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_nodejs.dir/build
.PHONY : rosgraph_msgs_generate_messages_nodejs/fast

# Convenience name for target.
speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/rule
.PHONY : speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/rule

# Convenience name for target.
rosgraph_msgs_generate_messages_py: speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/rule

.PHONY : rosgraph_msgs_generate_messages_py

# fast build rule for target.
rosgraph_msgs_generate_messages_py/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/build.make speech_command/CMakeFiles/rosgraph_msgs_generate_messages_py.dir/build
.PHONY : rosgraph_msgs_generate_messages_py/fast

# Convenience name for target.
speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/rule
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/rule

# Convenience name for target.
std_msgs_generate_messages_eus: speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/rule

.PHONY : std_msgs_generate_messages_eus

# fast build rule for target.
std_msgs_generate_messages_eus/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_eus.dir/build
.PHONY : std_msgs_generate_messages_eus/fast

# Convenience name for target.
speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/rule
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/rule

# Convenience name for target.
std_msgs_generate_messages_nodejs: speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/rule

.PHONY : std_msgs_generate_messages_nodejs

# fast build rule for target.
std_msgs_generate_messages_nodejs/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_nodejs.dir/build
.PHONY : std_msgs_generate_messages_nodejs/fast

# Convenience name for target.
speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/rule
.PHONY : speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/rule

# Convenience name for target.
roscpp_generate_messages_nodejs: speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/rule

.PHONY : roscpp_generate_messages_nodejs

# fast build rule for target.
roscpp_generate_messages_nodejs/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/build.make speech_command/CMakeFiles/roscpp_generate_messages_nodejs.dir/build
.PHONY : roscpp_generate_messages_nodejs/fast

# Convenience name for target.
speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/rule
.PHONY : speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/rule

# Convenience name for target.
std_msgs_generate_messages_py: speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/rule

.PHONY : std_msgs_generate_messages_py

# fast build rule for target.
std_msgs_generate_messages_py/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/build.make speech_command/CMakeFiles/std_msgs_generate_messages_py.dir/build
.PHONY : std_msgs_generate_messages_py/fast

src/AIUITester.o: src/AIUITester.cpp.o

.PHONY : src/AIUITester.o

# target to build an object file
src/AIUITester.cpp.o:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/AIUITester.cpp.o
.PHONY : src/AIUITester.cpp.o

src/AIUITester.i: src/AIUITester.cpp.i

.PHONY : src/AIUITester.i

# target to preprocess a source file
src/AIUITester.cpp.i:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/AIUITester.cpp.i
.PHONY : src/AIUITester.cpp.i

src/AIUITester.s: src/AIUITester.cpp.s

.PHONY : src/AIUITester.s

# target to generate assembly for a file
src/AIUITester.cpp.s:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/AIUITester.cpp.s
.PHONY : src/AIUITester.cpp.s

src/AudioPlayer.o: src/AudioPlayer.cpp.o

.PHONY : src/AudioPlayer.o

# target to build an object file
src/AudioPlayer.cpp.o:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/audio_player.dir/build.make speech_command/CMakeFiles/audio_player.dir/src/AudioPlayer.cpp.o
.PHONY : src/AudioPlayer.cpp.o

src/AudioPlayer.i: src/AudioPlayer.cpp.i

.PHONY : src/AudioPlayer.i

# target to preprocess a source file
src/AudioPlayer.cpp.i:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/audio_player.dir/build.make speech_command/CMakeFiles/audio_player.dir/src/AudioPlayer.cpp.i
.PHONY : src/AudioPlayer.cpp.i

src/AudioPlayer.s: src/AudioPlayer.cpp.s

.PHONY : src/AudioPlayer.s

# target to generate assembly for a file
src/AudioPlayer.cpp.s:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/audio_player.dir/build.make speech_command/CMakeFiles/audio_player.dir/src/AudioPlayer.cpp.s
.PHONY : src/AudioPlayer.cpp.s

src/AudioRecorder.o: src/AudioRecorder.cpp.o

.PHONY : src/AudioRecorder.o

# target to build an object file
src/AudioRecorder.cpp.o:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/audio_recorder.dir/build.make speech_command/CMakeFiles/audio_recorder.dir/src/AudioRecorder.cpp.o
.PHONY : src/AudioRecorder.cpp.o

src/AudioRecorder.i: src/AudioRecorder.cpp.i

.PHONY : src/AudioRecorder.i

# target to preprocess a source file
src/AudioRecorder.cpp.i:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/audio_recorder.dir/build.make speech_command/CMakeFiles/audio_recorder.dir/src/AudioRecorder.cpp.i
.PHONY : src/AudioRecorder.cpp.i

src/AudioRecorder.s: src/AudioRecorder.cpp.s

.PHONY : src/AudioRecorder.s

# target to generate assembly for a file
src/AudioRecorder.cpp.s:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/audio_recorder.dir/build.make speech_command/CMakeFiles/audio_recorder.dir/src/AudioRecorder.cpp.s
.PHONY : src/AudioRecorder.cpp.s

src/FileUtil.o: src/FileUtil.cpp.o

.PHONY : src/FileUtil.o

# target to build an object file
src/FileUtil.cpp.o:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/FileUtil.cpp.o
.PHONY : src/FileUtil.cpp.o

src/FileUtil.i: src/FileUtil.cpp.i

.PHONY : src/FileUtil.i

# target to preprocess a source file
src/FileUtil.cpp.i:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/FileUtil.cpp.i
.PHONY : src/FileUtil.cpp.i

src/FileUtil.s: src/FileUtil.cpp.s

.PHONY : src/FileUtil.s

# target to generate assembly for a file
src/FileUtil.cpp.s:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/FileUtil.cpp.s
.PHONY : src/FileUtil.cpp.s

src/TestListener.o: src/TestListener.cpp.o

.PHONY : src/TestListener.o

# target to build an object file
src/TestListener.cpp.o:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/TestListener.cpp.o
.PHONY : src/TestListener.cpp.o

src/TestListener.i: src/TestListener.cpp.i

.PHONY : src/TestListener.i

# target to preprocess a source file
src/TestListener.cpp.i:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/TestListener.cpp.i
.PHONY : src/TestListener.cpp.i

src/TestListener.s: src/TestListener.cpp.s

.PHONY : src/TestListener.s

# target to generate assembly for a file
src/TestListener.cpp.s:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/TestListener.cpp.s
.PHONY : src/TestListener.cpp.s

src/WriteAudioThread.o: src/WriteAudioThread.cpp.o

.PHONY : src/WriteAudioThread.o

# target to build an object file
src/WriteAudioThread.cpp.o:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/WriteAudioThread.cpp.o
.PHONY : src/WriteAudioThread.cpp.o

src/WriteAudioThread.i: src/WriteAudioThread.cpp.i

.PHONY : src/WriteAudioThread.i

# target to preprocess a source file
src/WriteAudioThread.cpp.i:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/WriteAudioThread.cpp.i
.PHONY : src/WriteAudioThread.cpp.i

src/WriteAudioThread.s: src/WriteAudioThread.cpp.s

.PHONY : src/WriteAudioThread.s

# target to generate assembly for a file
src/WriteAudioThread.cpp.s:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/AIUITester.dir/build.make speech_command/CMakeFiles/AIUITester.dir/src/WriteAudioThread.cpp.s
.PHONY : src/WriteAudioThread.cpp.s

src/aiuiMain.o: src/aiuiMain.cpp.o

.PHONY : src/aiuiMain.o

# target to build an object file
src/aiuiMain.cpp.o:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/speech_command_node.dir/build.make speech_command/CMakeFiles/speech_command_node.dir/src/aiuiMain.cpp.o
.PHONY : src/aiuiMain.cpp.o

src/aiuiMain.i: src/aiuiMain.cpp.i

.PHONY : src/aiuiMain.i

# target to preprocess a source file
src/aiuiMain.cpp.i:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/speech_command_node.dir/build.make speech_command/CMakeFiles/speech_command_node.dir/src/aiuiMain.cpp.i
.PHONY : src/aiuiMain.cpp.i

src/aiuiMain.s: src/aiuiMain.cpp.s

.PHONY : src/aiuiMain.s

# target to generate assembly for a file
src/aiuiMain.cpp.s:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f speech_command/CMakeFiles/speech_command_node.dir/build.make speech_command/CMakeFiles/speech_command_node.dir/src/aiuiMain.cpp.s
.PHONY : src/aiuiMain.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... audio_player"
	@echo "... list_install_components"
	@echo "... roscpp_generate_messages_py"
	@echo "... roscpp_generate_messages_eus"
	@echo "... edit_cache"
	@echo "... roscpp_generate_messages_lisp"
	@echo "... rosgraph_msgs_generate_messages_cpp"
	@echo "... audio_recorder"
	@echo "... std_msgs_generate_messages_lisp"
	@echo "... roscpp_generate_messages_cpp"
	@echo "... install"
	@echo "... rosgraph_msgs_generate_messages_eus"
	@echo "... rosgraph_msgs_generate_messages_lisp"
	@echo "... speech_command_node"
	@echo "... AIUITester"
	@echo "... std_msgs_generate_messages_cpp"
	@echo "... rosgraph_msgs_generate_messages_nodejs"
	@echo "... rosgraph_msgs_generate_messages_py"
	@echo "... std_msgs_generate_messages_eus"
	@echo "... std_msgs_generate_messages_nodejs"
	@echo "... roscpp_generate_messages_nodejs"
	@echo "... std_msgs_generate_messages_py"
	@echo "... src/AIUITester.o"
	@echo "... src/AIUITester.i"
	@echo "... src/AIUITester.s"
	@echo "... src/AudioPlayer.o"
	@echo "... src/AudioPlayer.i"
	@echo "... src/AudioPlayer.s"
	@echo "... src/AudioRecorder.o"
	@echo "... src/AudioRecorder.i"
	@echo "... src/AudioRecorder.s"
	@echo "... src/FileUtil.o"
	@echo "... src/FileUtil.i"
	@echo "... src/FileUtil.s"
	@echo "... src/TestListener.o"
	@echo "... src/TestListener.i"
	@echo "... src/TestListener.s"
	@echo "... src/WriteAudioThread.o"
	@echo "... src/WriteAudioThread.i"
	@echo "... src/WriteAudioThread.s"
	@echo "... src/aiuiMain.o"
	@echo "... src/aiuiMain.i"
	@echo "... src/aiuiMain.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/ucar_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

