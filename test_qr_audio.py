#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys

def test_audio_files():
    """测试音频文件播放功能"""
    print("===== 测试二维码音频播放功能 =====")
    
    # 定义音频文件路径映射
    audio_files = {
        "Fruit": "/home/<USER>/ucar_ws/my_code/voice/1_Fruit.wav",
        "Dessert": "/home/<USER>/ucar_ws/my_code/voice/1_Dessert.wav", 
        "Vegetable": "/home/<USER>/ucar_ws/my_code/voice/1_Vegetable.wav"
    }
    
    # 检查所有音频文件是否存在
    print("\n1. 检查音频文件是否存在:")
    all_exist = True
    for qr_content, audio_file in audio_files.items():
        if os.path.exists(audio_file):
            print(f"  ✓ {qr_content}: {audio_file}")
        else:
            print(f"  ✗ {qr_content}: {audio_file} (文件不存在)")
            all_exist = False
    
    if not all_exist:
        print("\n错误: 部分音频文件不存在，请检查文件路径")
        return False
    
    # 测试播放功能
    print("\n2. 测试音频播放功能:")
    for qr_content, audio_file in audio_files.items():
        print(f"\n播放 {qr_content} 音频...")
        print(f"文件: {audio_file}")
        
        try:
            # 使用aplay播放WAV文件
            result = os.system(f"aplay {audio_file}")
            if result == 0:
                print(f"  ✓ {qr_content} 音频播放成功")
            else:
                print(f"  ✗ {qr_content} 音频播放失败，返回码: {result}")
        except Exception as e:
            print(f"  ✗ {qr_content} 音频播放异常: {str(e)}")
        
        # 询问是否继续
        if qr_content != "Vegetable":  # 最后一个不询问
            response = input("按回车键继续播放下一个音频，或输入 'q' 退出: ")
            if response.lower() == 'q':
                break
    
    print("\n===== 测试完成 =====")
    return True

def play_specific_audio(qr_content):
    """播放指定的音频文件"""
    audio_files = {
        "Fruit": "/home/<USER>/ucar_ws/my_code/voice/1_Fruit.wav",
        "Dessert": "/home/<USER>/ucar_ws/my_code/voice/1_Dessert.wav", 
        "Vegetable": "/home/<USER>/ucar_ws/my_code/voice/1_Vegetable.wav"
    }
    
    if qr_content in audio_files:
        audio_file = audio_files[qr_content]
        if os.path.exists(audio_file):
            print(f"播放 {qr_content} 音频: {audio_file}")
            os.system(f"aplay {audio_file}")
        else:
            print(f"音频文件不存在: {audio_file}")
    else:
        print(f"未识别的内容: {qr_content}")
        print(f"支持的内容: {list(audio_files.keys())}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 如果提供了参数，播放指定的音频
        qr_content = sys.argv[1]
        play_specific_audio(qr_content)
    else:
        # 否则运行完整测试
        test_audio_files()
