#!/bin/bash
# 播放音频文件的脚本

AUDIO_FILE="/home/<USER>/ucar_ws/my_code/voice/1_fruit.mp3"

echo "尝试播放音频文件: $AUDIO_FILE"

# 检查文件是否存在
if [ ! -f "$AUDIO_FILE" ]; then
    echo "错误: 音频文件不存在"
    exit 1
fi

echo "文件存在，开始播放..."

# 方法1: 使用 mplayer (无界面模式)
if command -v mplayer >/dev/null 2>&1; then
    echo "使用 mplayer 播放..."
    mplayer -really-quiet -nolirc "$AUDIO_FILE"
    exit 0
fi

# 方法2: 使用 play (sox工具)
if command -v play >/dev/null 2>&1; then
    echo "使用 play 播放..."
    play "$AUDIO_FILE"
    exit 0
fi

# 方法3: 使用 ffplay
if command -v ffplay >/dev/null 2>&1; then
    echo "使用 ffplay 播放..."
    ffplay -nodisp -autoexit "$AUDIO_FILE"
    exit 0
fi

# 方法4: 使用 mpg123
if command -v mpg123 >/dev/null 2>&1; then
    echo "使用 mpg123 播放..."
    mpg123 "$AUDIO_FILE"
    exit 0
fi

echo "错误: 没有找到可用的音频播放器"
echo "请安装以下任一播放器: mplayer, sox, ffmpeg, mpg123"
exit 1
