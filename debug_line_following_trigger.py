#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os

class LineFollowingDebugger:
    """线跟踪触发调试器"""
    
    def __init__(self):
        # 线跟踪脚本路径
        self.line_following_script = "/home/<USER>/ucar_ws/my_code/run_line_following.sh"
        
        # 导航点配置
        self.waypoints_array = [
            [-12.6, 1.45, -0.682, ["detect_white_line", "scan_qrcode"], 0, 30],  # 索引0: 点1
            [-14.1, -0.8, -0.682, ["rotate_360_detect_banana"], 0, 60],          # 索引1: 点2
            [-15.922, -0.447, -2.2, "detect_traffic_light", 0, 30],              # 索引2: 红绿灯1
            [-16.884, 0.516, -2.2, "detect_traffic_light", 0, 30],               # 索引3: 红绿灯2
            [-15.400, -0.100, 1.098, "", 0, 30],                                 # 索引4: 路口1
            [-16.891, 1.113, 0.550, "", 0, 30],                                  # 索引5: 路口2
        ]
    
    def check_line_following_script(self):
        """检查线跟踪脚本状态"""
        print("🔍 检查线跟踪脚本状态:")
        print("=" * 60)
        
        script_path = self.line_following_script
        
        # 检查文件是否存在
        if os.path.exists(script_path):
            print(f"✅ 脚本文件存在: {script_path}")
            
            # 检查执行权限
            if os.access(script_path, os.X_OK):
                print(f"✅ 脚本有执行权限")
            else:
                print(f"❌ 脚本没有执行权限")
                print(f"   解决方法: chmod +x {script_path}")
            
            # 检查文件大小
            file_size = os.path.getsize(script_path)
            print(f"📊 脚本文件大小: {file_size} 字节")
            
            # 检查脚本内容（前几行）
            try:
                with open(script_path, 'r') as f:
                    lines = f.readlines()[:5]
                print(f"📄 脚本前5行:")
                for i, line in enumerate(lines, 1):
                    print(f"   {i}: {line.strip()}")
            except Exception as e:
                print(f"❌ 读取脚本内容失败: {e}")
                
        else:
            print(f"❌ 脚本文件不存在: {script_path}")
        
        print("=" * 60)
    
    def simulate_navigate_to_waypoint(self, index):
        """模拟navigate_to_waypoint函数的线跟踪触发逻辑"""
        waypoint_names = ["点1", "点2", "红绿灯1", "红绿灯2", "路口1", "路口2"]
        
        print(f"\n🚗 模拟导航到{waypoint_names[index]} (索引{index})")
        print(f"   坐标: {self.waypoints_array[index][:3]}")
        
        # 模拟导航完成
        print("   ✅ 导航完成")
        
        # 检查是否到达路口1或路口2，如果是则运行线跟踪脚本
        if index == 4 or index == 5:  # 索引4是路口1，索引5是路口2
            intersection_name = "路口1" if index == 4 else "路口2"
            print("\n" + "="*50)
            print("【线跟踪启动】: 到达%s，启动线跟踪程序" % intersection_name)
            print("="*50 + "\n")
            
            # 运行线跟踪脚本
            self.run_line_following_script(intersection_name)
            return True
        else:
            print(f"   ℹ️  索引{index}不是路口，不触发线跟踪")
            return False
    
    def run_line_following_script(self, intersection_name):
        """运行线跟踪脚本（调试版本）"""
        print(f"【线跟踪】: 在{intersection_name}启动线跟踪程序")
        
        # 线跟踪脚本路径
        script_path = self.line_following_script
        
        # 检查脚本是否存在
        if os.path.exists(script_path):
            try:
                print(f"【线跟踪】: 执行脚本: {script_path}")
                print(f"【线跟踪】: 正在启动线跟踪程序...")
                print(f"【线跟踪】: 脚本将在后台运行")
                print(f"【线跟踪】: 如需停止，请手动终止线跟踪程序")
                
                # 使用subprocess运行脚本，这样可以更好地控制进程
                import subprocess
                
                # 在后台运行脚本
                print(f"【线跟踪】: 执行命令: {script_path}")
                process = subprocess.Popen([script_path],
                                         shell=True,
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE)
                
                print(f"【线跟踪】: 脚本已启动，进程ID: {process.pid}")
                print(f"【线跟踪】: 导航任务完成，线跟踪程序接管控制")
                
                # 等待一小段时间确保脚本启动
                import time
                time.sleep(2)
                
                # 检查进程是否还在运行
                if process.poll() is None:
                    print(f"【线跟踪】: ✅ 线跟踪程序运行正常")
                else:
                    print(f"【线跟踪】: ❌ 警告 - 线跟踪程序可能已退出")
                    # 读取错误输出
                    stdout, stderr = process.communicate()
                    if stderr:
                        print(f"【线跟踪】: 错误输出: {stderr.decode()}")
                
            except Exception as e:
                print(f"【线跟踪】: ❌ 启动失败 - {str(e)}")
        else:
            print(f"【线跟踪】: ❌ 脚本文件不存在 - {script_path}")
    
    def test_all_waypoints(self):
        """测试所有导航点的线跟踪触发"""
        print("\n🧪 测试所有导航点的线跟踪触发:")
        print("=" * 60)
        
        waypoint_names = ["点1", "点2", "红绿灯1", "红绿灯2", "路口1", "路口2"]
        
        for i in range(len(self.waypoints_array)):
            print(f"\n测试 {i+1}/{len(self.waypoints_array)}: {waypoint_names[i]}")
            triggered = self.simulate_navigate_to_waypoint(i)
            if triggered:
                print(f"   ✅ 线跟踪已触发")
            else:
                print(f"   ⏭️  跳过（非路口）")
        
        print("\n" + "=" * 60)
    
    def test_specific_intersection(self, intersection_index):
        """测试特定路口的线跟踪触发"""
        waypoint_names = ["点1", "点2", "红绿灯1", "红绿灯2", "路口1", "路口2"]
        
        if intersection_index in [4, 5]:
            print(f"\n🎯 专门测试{waypoint_names[intersection_index]}的线跟踪触发:")
            print("=" * 60)
            self.simulate_navigate_to_waypoint(intersection_index)
            print("=" * 60)
        else:
            print(f"❌ 索引{intersection_index}不是路口（路口索引为4和5）")
    
    def check_navigation_logic(self):
        """检查导航逻辑"""
        print("\n🔍 检查导航逻辑:")
        print("=" * 60)
        
        print("📍 导航点配置:")
        waypoint_names = ["点1", "点2", "红绿灯1", "红绿灯2", "路口1", "路口2"]
        for i, (waypoint, name) in enumerate(zip(self.waypoints_array, waypoint_names)):
            actions = waypoint[3] if len(waypoint) > 3 and waypoint[3] else "无"
            line_following = " → 🚗线跟踪" if i in [4, 5] else ""
            print(f"  索引{i}: {name} - {waypoint[:3]} - 动作: {actions}{line_following}")
        
        print("\n🔄 线跟踪触发条件:")
        print("  - 条件: index == 4 or index == 5")
        print("  - 索引4: 路口1")
        print("  - 索引5: 路口2")
        
        print("\n📋 预期行为:")
        print("  1. 红绿灯1是绿灯 → 导航到路口1(索引4) → 播放音频 → 启动线跟踪")
        print("  2. 红绿灯1是红灯 → 导航到红绿灯2(索引3) → 播放音频 → 导航到路口2(索引5) → 启动线跟踪")
        
        print("=" * 60)
    
    def run_debug(self):
        """运行完整调试"""
        print("🚀 线跟踪触发调试器")
        print("=" * 60)
        
        # 检查脚本状态
        self.check_line_following_script()
        
        # 检查导航逻辑
        self.check_navigation_logic()
        
        # 测试所有导航点
        input("\n按回车键测试所有导航点的线跟踪触发...")
        self.test_all_waypoints()
        
        # 专门测试路口1
        input("\n按回车键专门测试路口1的线跟踪触发...")
        self.test_specific_intersection(4)
        
        # 专门测试路口2
        input("\n按回车键专门测试路口2的线跟踪触发...")
        self.test_specific_intersection(5)
        
        print("\n🎉 调试完成！")
        print("\n📝 调试总结:")
        print("   ✅ 检查了线跟踪脚本的存在性和权限")
        print("   ✅ 验证了线跟踪触发条件（索引4和5）")
        print("   ✅ 测试了路口1和路口2的线跟踪启动")
        print("\n💡 如果实际运行时没有触发线跟踪，可能的原因:")
        print("   1. 导航没有到达路口1或路口2（检查导航逻辑）")
        print("   2. 线跟踪脚本没有执行权限")
        print("   3. 线跟踪脚本启动后立即退出（检查脚本内容）")
        print("   4. ROS环境或依赖问题")

if __name__ == "__main__":
    debugger = LineFollowingDebugger()
    debugger.run_debug()
