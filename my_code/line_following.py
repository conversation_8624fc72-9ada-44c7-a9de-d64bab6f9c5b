#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
白色胶带循迹程序 - 用于在深色地面上跟踪白色胶带路径
"""

import cv2
import numpy as np
import time
import argparse
import rospy
from geometry_msgs.msg import Twist
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import threading
import queue
from multiprocessing import Process, Queue, Value

class LineFollowingNode:
    """循迹节点类 - 订阅校正后的图像并进行循迹控制"""
    def __init__(self, args):
        # 参数设置
        self.args = args

        # 创建CV Bridge
        self.bridge = CvBridge()

        # 当前图像
        self.current_frame = None
        self.frame_received = False

        # 创建速度发布者
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)

        # 订阅校正后的图像话题
        self.image_sub = rospy.Subscriber(args.image_topic, Image, self.image_callback)

        # 计算图像中心点 (将在收到第一帧图像后设置)
        self.center_x = None
        self.image_width = None
        self.image_height = None

        # 创建PID控制器 (将在收到第一帧图像后初始化)
        self.pid = None

        # 控制变量
        self.control_enabled = True
        self.last_line_position = None
        self.line_lost_time = 0
        self.line_lost_timeout = 3.0  # 白线丢失超时时间（秒）
        self.program_should_exit = False  # 程序退出标志

        # 帧率计算变量
        self.frame_count = 0
        self.start_time = time.time()
        self.fps = 0

        # 当前使用的阈值
        self.current_threshold = args.threshold
        self.original_otsu_threshold = None

        # 多核显示相关
        self.display_queue = Queue(maxsize=2) if args.display else None
        self.stop_flag = Value('i', 0) if args.display else None
        self.display_process = None

        # 横向直线检测
        self.horizontal_line_detected = False
        self.stop_line_coords = None

        print("等待校正后的图像...")
        print(f"订阅话题: {args.image_topic}")
        threshold_method = "手动阈值" if args.manual_threshold else "大津法自动阈值 (+40, 最大200)"
        print(f"阈值方法: {threshold_method}")
        if args.manual_threshold:
            print(f"手动阈值: {args.threshold}")
        else:
            print("大津法阈值将自动计算，并在基础上加40（最大不超过200）")

        skeleton_status = "启用" if not args.no_skeleton else "禁用"
        print(f"骨架化处理: {skeleton_status}")
        if not args.no_skeleton:
            print("胶带将被简化为中心线，便于精确定位")

        stop_detection_status = "启用" if not args.disable_stop_detection else "禁用"
        print(f"横向停车线检测: {stop_detection_status}")
        if not args.disable_stop_detection:
            print("检测到横向直线时将自动停车并退出程序")

        if args.display:
            print("多核显示处理: 启用 (提高帧率)")

        print("按 'q' 键退出")
        print("按 'c' 键切换控制模式（开/关）")

    def image_callback(self, msg):
        """图像回调函数"""
        try:
            # 将ROS图像消息转换为OpenCV图像
            self.current_frame = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            self.frame_received = True

            # 如果是第一次收到图像，初始化参数
            if self.center_x is None:
                self.image_height, self.image_width = self.current_frame.shape[:2]
                self.center_x = self.image_width // 2
                self.last_line_position = self.center_x

                # 创建PID控制器
                self.pid = PIDController(kp=self.args.kp, ki=self.args.ki, kd=self.args.kd, setpoint=self.center_x)

                print(f"收到图像，分辨率: {self.image_width}x{self.image_height}")
                print("开始循迹...")

        except Exception as e:
            rospy.logerr(f"图像转换错误: {e}")

    def process_frame(self):
        """处理当前帧"""
        if not self.frame_received or self.current_frame is None:
            return

        frame = self.current_frame.copy()

        # 截取图像下半部分进行处理
        height, width = frame.shape[:2]
        crop_ratio = getattr(self.args, 'crop_ratio', 0.3)  # 默认从30%位置开始截取（截取下70%）
        crop_start_y = int(height * crop_ratio)  # 根据比例计算截取起始位置
        cropped_frame = frame[crop_start_y:, :]  # 截取指定部分

        # 检测白色胶带（使用截取后的图像）
        use_otsu = not self.args.manual_threshold  # 如果指定手动阈值，则不使用Otsu
        use_skeleton = not self.args.no_skeleton  # 如果指定不使用骨架化，则不使用
        binary_image, actual_threshold, original_otsu = detect_line(cropped_frame, self.args.threshold, use_otsu=use_otsu, use_skeleton=use_skeleton)

        # 保存实际使用的阈值用于显示
        self.current_threshold = actual_threshold
        self.original_otsu_threshold = original_otsu if use_otsu else None

        # 检测横向直线（停车线）- 如果启用的话
        if not self.args.disable_stop_detection:
            horizontal_detected, stop_coords = detect_horizontal_line(binary_image, min_line_length=int(cropped_frame.shape[1] * 0.6))

            if horizontal_detected and not self.horizontal_line_detected:
                self.horizontal_line_detected = True
                self.stop_line_coords = stop_coords
                print("\n检测到横向停车线！准备停车...")

        # 查找白色胶带的位置
        line_position, line_detected, roi_y, left_line_pos, right_line_pos = find_line_position(binary_image)

        # 如果检测到白色胶带，更新位置
        if line_detected:
            self.last_line_position = line_position
            self.line_lost_time = 0
        else:
            # 如果没有检测到白色胶带，记录丢失时间
            if self.line_lost_time == 0:
                self.line_lost_time = time.time()

        # 计算PID输出
        error = self.center_x - self.last_line_position
        output, terms = self.pid.compute(self.last_line_position)

        # 计算帧率
        self.frame_count += 1
        if self.frame_count >= 10:
            end_time = time.time()
            self.fps = self.frame_count / (end_time - self.start_time)
            self.frame_count = 0
            self.start_time = time.time()

        # 发送控制命令
        self.send_control_command(output)

        # 显示视觉反馈
        if self.args.display:
            self.display_visualization(frame, cropped_frame, binary_image, line_position, line_detected,
                                     roi_y, left_line_pos, right_line_pos, error, output, crop_start_y)

    def send_control_command(self, output):
        """发送控制命令"""
        twist = Twist()

        # 如果检测到横向停车线，立即停车并退出
        if self.horizontal_line_detected:
            twist.linear.x = 0.0
            twist.angular.z = 0.0
            self.cmd_vel_pub.publish(twist)
            print("\n检测到停车线，小车已停止！程序将退出...")
            # 设置停止标志
            if self.stop_flag:
                self.stop_flag.value = 1
            return

        if self.control_enabled:
            # 如果白色胶带丢失时间过长，停止小车并准备退出程序
            if self.line_lost_time > 0 and time.time() - self.line_lost_time > self.line_lost_timeout:
                # 发送停止命令
                twist.linear.x = 0.0
                twist.angular.z = 0.0
                self.cmd_vel_pub.publish(twist)

                # 设置程序退出标志
                self.program_should_exit = True

                print(f"\n白色胶带丢失超过{self.line_lost_timeout}秒，程序将退出")
                print("白色胶带丢失，已停止小车")

                # 设置停止标志
                if self.stop_flag:
                    self.stop_flag.value = 1
                return
            else:
                # 设置线速度
                twist.linear.x = self.args.speed

                # 将PID输出转换为角速度
                angular_z = -output / 100.0

                # 限制角速度范围
                max_angular = 1.0
                angular_z = max(-max_angular, min(max_angular, angular_z))

                # 发送速度命令
                twist.angular.z = angular_z
                self.cmd_vel_pub.publish(twist)

                # 打印控制信息
                error = self.center_x - self.last_line_position
                print(f"\r偏差: {error:+d} 像素, PID输出: {output:.2f}, 角速度: {angular_z:.2f}", end="")
        else:
            # 停止小车
            twist.linear.x = 0.0
            twist.angular.z = 0.0
            self.cmd_vel_pub.publish(twist)

    def display_visualization(self, frame, cropped_frame, binary_image, line_position, line_detected,
                            roi_y, left_line_pos, right_line_pos, error, output, crop_start_y):
        """显示视觉反馈 - 只显示截取后的图像和二值化图像"""

        # 显示截取后的彩色图像和二值化图像
        cropped_display = cropped_frame.copy()
        binary_display = cv2.cvtColor(binary_image, cv2.COLOR_GRAY2BGR)

        # 获取截取后图像的尺寸
        crop_height, crop_width = cropped_frame.shape[:2]

        # 在截取后的图像上标记ROI区域
        cv2.line(cropped_display, (0, roi_y), (crop_width, roi_y), (0, 255, 0), 2)
        cv2.line(cropped_display, (0, roi_y+20), (crop_width, roi_y+20), (0, 255, 0), 2)
        cv2.line(binary_display, (0, roi_y), (crop_width, roi_y), (0, 255, 0), 2)
        cv2.line(binary_display, (0, roi_y+20), (crop_width, roi_y+20), (0, 255, 0), 2)

        # 在截取后的图像上标记中心线
        cv2.line(cropped_display, (self.center_x, 0), (self.center_x, crop_height), (0, 0, 255), 2)
        cv2.line(binary_display, (self.center_x, 0), (self.center_x, crop_height), (0, 0, 255), 2)

        # 添加图像标题和状态信息
        crop_percentage = int((1 - self.args.crop_ratio) * 100)
        cv2.putText(cropped_display, f"Cropped Image (bottom {crop_percentage}%) - FPS: {self.fps:.1f}",
                   (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # 显示当前使用的阈值
        if self.args.manual_threshold:
            cv2.putText(cropped_display, f"Manual Threshold: {self.current_threshold:.1f}",
                       (10, crop_height - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        else:
            # 显示大津法原始阈值和调整后阈值
            if self.original_otsu_threshold is not None:
                cv2.putText(cropped_display, f"Otsu: {self.original_otsu_threshold:.1f} -> {self.current_threshold:.1f} (+40, max200)",
                           (10, crop_height - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)

        # 添加控制状态
        status_text = "ENABLED" if self.control_enabled else "DISABLED"
        status_color = (0, 255, 0) if self.control_enabled else (0, 0, 255)
        cv2.putText(cropped_display, f"Control: {status_text}",
                   (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)

        # 添加检测状态
        if line_detected:
            cv2.putText(cropped_display, f"Line Detected - Error: {error:+d}px",
                       (10, 75), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        else:
            cv2.putText(cropped_display, "No Line Detected",
                       (10, 75), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

        # 显示二值化图像标题，包含骨架化状态
        skeleton_status = "Skeletonized" if not self.args.no_skeleton else "Original Width"
        cv2.putText(binary_display, f"Binary Image ({skeleton_status})",
                   (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # 在截取后的图像上显示检测到的线条
        if line_detected:
            # 在截取后的彩色图像上显示线条
            cv2.line(cropped_display, (line_position, roi_y), (line_position, roi_y+20), (255, 0, 0), 3)
            cv2.line(cropped_display, (self.center_x, roi_y+10), (line_position, roi_y+10), (255, 0, 0), 2)

            # 在二值化图像上显示线条
            cv2.line(binary_display, (line_position, roi_y), (line_position, roi_y+20), (255, 0, 0), 3)

            # 显示左右两条线（如果检测到）
            if left_line_pos is not None:
                cv2.line(cropped_display, (left_line_pos, roi_y), (left_line_pos, roi_y+20), (0, 255, 0), 3)
                cv2.line(binary_display, (left_line_pos, roi_y), (left_line_pos, roi_y+20), (0, 255, 0), 3)
            if right_line_pos is not None:
                cv2.line(cropped_display, (right_line_pos, roi_y), (right_line_pos, roi_y+20), (0, 255, 0), 3)
                cv2.line(binary_display, (right_line_pos, roi_y), (right_line_pos, roi_y+20), (0, 255, 0), 3)

            # 如果检测到两条线，显示连接线
            if left_line_pos is not None and right_line_pos is not None:
                cv2.line(cropped_display, (left_line_pos, roi_y+10), (right_line_pos, roi_y+10), (255, 255, 0), 2)
                cv2.line(binary_display, (left_line_pos, roi_y+10), (right_line_pos, roi_y+10), (255, 255, 0), 2)

        # 显示横向停车线（如果检测到）
        if self.horizontal_line_detected and self.stop_line_coords:
            x1, y1, x2, y2 = self.stop_line_coords
            cv2.line(cropped_display, (x1, y1), (x2, y2), (0, 0, 255), 4)
            cv2.line(binary_display, (x1, y1), (x2, y2), (0, 0, 255), 4)
            cv2.putText(cropped_display, "STOP LINE DETECTED!", (10, 100),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)

        # 组合截取后的图像和二值化图像
        combined = np.vstack((cropped_display, binary_display))

        # 调整显示大小
        display_height = 600
        display_width = int(combined.shape[1] * (display_height / combined.shape[0]))
        combined_resized = cv2.resize(combined, (display_width, display_height))

        # 使用多核处理显示图像
        if self.display_queue is not None:
            try:
                # 非阻塞方式放入队列，如果队列满了就跳过这一帧
                if not self.display_queue.full():
                    self.display_queue.put_nowait(combined_resized)
            except:
                pass  # 队列满了，跳过这一帧

    def run(self):
        """运行循迹节点"""
        # 启动多核显示进程
        if self.args.display:
            self.display_process = Process(target=display_worker, args=(self.display_queue, self.stop_flag))
            self.display_process.start()
            print("多核显示进程已启动")

        rate = rospy.Rate(60)  # 提高到60Hz以获得更好的帧率

        while not rospy.is_shutdown():
            # 检查是否需要停止（横向直线检测或用户按键）
            if self.stop_flag and self.stop_flag.value:
                print("\n收到停止信号，程序退出")
                break

            # 如果检测到横向停车线，退出循环
            if self.horizontal_line_detected:
                time.sleep(2)  # 等待2秒确保停车
                break

            # 处理当前帧
            self.process_frame()

            # 简化按键处理，主要由显示进程处理
            if self.args.display and self.stop_flag and self.stop_flag.value:
                break

            rate.sleep()

        # 停止小车
        twist = Twist()
        twist.linear.x = 0.0
        twist.angular.z = 0.0
        self.cmd_vel_pub.publish(twist)

        # 清理多核显示进程
        if self.args.display and self.display_process:
            print("正在停止显示进程...")
            if self.stop_flag:
                self.stop_flag.value = 1

            # 等待进程正常退出
            self.display_process.join(timeout=3)

            # 如果进程还在运行，强制终止
            if self.display_process.is_alive():
                print("强制终止显示进程...")
                self.display_process.terminate()
                self.display_process.join(timeout=1)

            # 如果还是没有退出，强制杀死
            if self.display_process.is_alive():
                print("强制杀死显示进程...")
                self.display_process.kill()

            print("多核显示进程已停止")

        # 确保ROS节点正常关闭
        try:
            rospy.signal_shutdown("程序正常退出")
        except:
            pass

        print("\n程序已退出，小车已停止")

        # 强制退出程序
        import os
        import signal
        os.kill(os.getpid(), signal.SIGTERM)

class PIDController:
    """简单的PID控制器"""
    def __init__(self, kp=0.1, ki=0.1, kd=0.0, setpoint=0):
        self.kp = kp  # 比例增益
        self.ki = ki  # 积分增益
        self.kd = kd  # 微分增益
        self.setpoint = setpoint  # 设定值
        self.error_sum = 0  # 误差累积
        self.last_error = 0  # 上一次误差
        self.last_time = time.time()  # 上一次时间
        self.output_limits = (-100, 100)  # 输出限制

    def compute(self, process_variable):
        """计算PID输出"""
        # 计算时间差
        current_time = time.time()
        dt = current_time - self.last_time
        if dt <= 0:
            dt = 0.1  # 防止除零错误

        # 计算误差
        error = self.setpoint - process_variable

        # 计算比例项
        p_term = self.kp * error

        # 计算积分项
        self.error_sum += error * dt
        i_term = self.ki * self.error_sum

        # 计算微分项
        d_term = 0
        if dt > 0:
            d_term = self.kd * (error - self.last_error) / dt

        # 计算总输出
        output = p_term + i_term + d_term

        # 限制输出范围
        if output > self.output_limits[1]:
            output = self.output_limits[1]
        elif output < self.output_limits[0]:
            output = self.output_limits[0]

        # 更新状态
        self.last_error = error
        self.last_time = current_time

        return output, (p_term, i_term, d_term)

def skeletonize(binary_image):
    """骨架化算法 - 将胶带简化为中心线"""
    # 使用形态学操作进行骨架化
    # 创建结构元素
    kernel = cv2.getStructuringElement(cv2.MORPH_CROSS, (3, 3))

    # 复制输入图像
    skeleton = np.zeros_like(binary_image)
    temp = binary_image.copy()

    # 迭代骨架化
    while True:
        # 腐蚀
        eroded = cv2.erode(temp, kernel)
        # 开运算
        opened = cv2.morphologyEx(eroded, cv2.MORPH_OPEN, kernel)
        # 计算骨架的一部分
        subset = cv2.subtract(eroded, opened)
        # 添加到骨架
        skeleton = cv2.bitwise_or(skeleton, subset)
        # 更新temp
        temp = eroded.copy()

        # 如果没有更多像素可以腐蚀，停止
        if cv2.countNonZero(temp) == 0:
            break

    return skeleton

def detect_horizontal_line(binary_image, min_line_length=100):
    """检测横向直线 - 用于停车检测"""
    height, width = binary_image.shape

    # 使用霍夫直线变换检测直线
    lines = cv2.HoughLinesP(binary_image, 1, np.pi/180, threshold=50,
                           minLineLength=min_line_length, maxLineGap=20)

    if lines is not None:
        for line in lines:
            x1, y1, x2, y2 = line[0]
            # 计算直线角度
            angle = np.abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)

            # 检查是否为横向直线（角度接近0度或180度）
            if angle < 15 or angle > 165:
                line_length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
                # 如果横向直线足够长，认为检测到停车线
                if line_length > min_line_length:
                    return True, (x1, y1, x2, y2)

    return False, None

def display_worker(display_queue, stop_flag):
    """多核显示处理工作进程"""
    try:
        cv2.namedWindow("Line Following", cv2.WINDOW_NORMAL)

        while not stop_flag.value:
            try:
                # 从队列获取显示数据，设置超时避免阻塞
                if not display_queue.empty():
                    display_data = display_queue.get_nowait()
                    if display_data is not None:
                        combined_image = display_data
                        cv2.imshow("Line Following", combined_image)

                # 处理按键事件
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    stop_flag.value = 1
                    break

            except Exception as e:
                print(f"显示进程错误: {e}")
                break

    except Exception as e:
        print(f"显示进程初始化错误: {e}")
    finally:
        # 确保窗口被关闭
        try:
            cv2.destroyAllWindows()
        except:
            pass
        print("显示进程正常退出")

def detect_line(frame, threshold_value=200, use_otsu=True, use_skeleton=True):
    """检测白色胶带"""
    # 转换为灰度图
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # 应用高斯模糊减少噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    if use_otsu:
        # 使用大津法自动获取阈值
        otsu_threshold, _ = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 在大津法阈值基础上加40，但不超过200
        adjusted_threshold = min(otsu_threshold + 40, 200)

        # 使用调整后的阈值进行二值化
        _, thresh = cv2.threshold(blurred, adjusted_threshold, 255, cv2.THRESH_BINARY)

        # 可以选择性地打印阈值用于调试
        # print(f"Otsu阈值: {otsu_threshold:.1f}, 调整后阈值: {adjusted_threshold:.1f}")

        if use_skeleton:
            # 骨架化处理，将胶带简化为中心线
            thresh = skeletonize(thresh)

        return thresh, adjusted_threshold, otsu_threshold
    else:
        # 使用手动阈值
        _, thresh = cv2.threshold(blurred, threshold_value, 255, cv2.THRESH_BINARY)

        if use_skeleton:
            # 骨架化处理，将胶带简化为中心线
            thresh = skeletonize(thresh)

        return thresh, threshold_value, threshold_value
huan_flag=0
huan_num=0
def find_line_position(binary_image, roi_height=20):
    global huan_flag, huan_num
    huan_height=40
    zhen_jian=10
    """查找两条白色胶带之间的中心线"""
    height, width = binary_image.shape
    roi_y = int(height * 0.3)
    # 定义感兴趣区域（ROI）
    if huan_flag==1:
        roi_y = int(height * 0.4)
        print("环岛中" + str(huan_num))
        huan_num-=0.1
        if huan_num<0:
            huan_flag=2#待出环
    elif huan_flag==2:
        roi_y = int(height * 0.4)
        print("待出环")
    elif huan_flag==3:
        roi_y = int(height * 0.3)
        print("出环中" + str(huan_num))
        huan_num-=0.2
        if huan_num<0:
            huan_flag=4
    roi = binary_image[roi_y:roi_y+roi_height, :]

    # 查找白色像素的位置
    white_pixels = np.where(roi == 255)

    if len(white_pixels[1]) > 0:
        # 使用直方图找到两条线
        histogram = np.sum(roi, axis=0)

        # 将直方图分成左右两部分
        midpoint = width // 2
        left_half = histogram[:midpoint]
        right_half = histogram[midpoint:]

        # 找到左右两侧的峰值位置
        left_peak = np.argmax(left_half) if np.max(left_half) > 0 else 0
        right_peak = np.argmax(right_half) + midpoint if np.max(right_half) > 0 else width

        # 检查否找到了两条是线
        left_line_detected = np.max(left_half) > 100  # 阈值可以调整
        right_line_detected = np.max(right_half) > 100  # 阈值可以调整

        # 计算中心线位置
        if left_line_detected and right_line_detected:
            if huan_flag==3:
                print("环岛结束")
                line_position = right_peak - 100
            else:
                # 如果检测到两条线，取它们的中点
                line_position = (left_peak + right_peak) // 2
            line_detected = True
        elif left_line_detected:
            # 只检测到左线，估计中心位置
            # 判断是否为圆环(靠近赛道的一边)
            roi_huan = binary_image[roi_y:roi_y+huan_height, :]
            huan_histogram = np.sum(roi_huan, axis=0)
            huan_right_half = huan_histogram[midpoint:]
            huan_right_line_detected = np.max(huan_right_half) > 100  # 阈值可以调整
            if not huan_right_line_detected and (huan_flag==0 or huan_flag==2):#向上40像素也还是0说明是圆环
                line_position = left_peak+60
                huan_num+=1
                if huan_flag==0 and huan_num>zhen_jian:
                    huan_flag=1
                elif huan_flag==2 and huan_num>zhen_jian:
                    huan_flag=3
                line_detected = True
            elif huan_flag:
                line_position = left_peak + 150  # 假设线间距为300像素
                line_detected = True
            else:
                line_position = left_peak + 100  # 假设线间距为300像素
                line_detected = True
        elif right_line_detected:
            print("hhhhhhhh")
            # 判断是否为圆环（远离赛道的一边）
            roi_huan = binary_image[roi_y:roi_y+huan_height, :]
            huan_histogram = np.sum(roi_huan, axis=0)
            huan_left_half = histogram[:midpoint]
            huan_right_line_detected = np.max(huan_left_half) > 100  # 阈值可以调整
            if huan_flag==2:
                huan_flag=3
                print("出环啦出环啦")
            if huan_flag==3 and huan_right_line_detected:
                print("出环")
                line_position = right_peak - 200
                line_detected = True
            else:
                # 只检测到右线，估计中心位置
                line_position = right_peak - 100  # 假设线间距为300像素
                line_detected = True
        else:
            if huan_flag==3:
                line_position=width // 2+100
            # 如果没有检测到线，使用图像中心
            else:
                line_position = width // 2
            line_detected = False

        # 返回两条线的位置以便于可视化
        return line_position, line_detected, roi_y, left_peak if left_line_detected else None, right_peak if right_line_detected else None
    else:
        # 如果没有检测到白色像素，返回图像中心
        line_position = width // 2
        line_detected = False
        return line_position, line_detected, roi_y, None, None

def signal_handler(signum, frame):
    """信号处理函数"""
    print(f"\n收到信号 {signum}，正在退出...")
    import os
    os._exit(0)

def main():
    # 设置信号处理
    import signal
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='白色胶带循迹程序 - 订阅校正后的图像')
    parser.add_argument('--image_topic', type=str, default='/usb_cam/image_calibrated',
                       help='图像话题名称 (默认: /usb_cam/image_calibrated)')
    parser.add_argument('--threshold', type=int, default=200, help='二值化阈值 (默认: 200)')
    parser.add_argument('--speed', type=float, default=0.2, help='前进速度 (默认: 0.2 m/s)')
    parser.add_argument('--kp', type=float, default=0.5, help='PID比例增益 (默认: 0.5)')
    parser.add_argument('--ki', type=float, default=0.0, help='PID积分增益 (默认: 0.0)')
    parser.add_argument('--kd', type=float, default=0.000, help='PID微分增益 (默认: 0.000)')
    parser.add_argument('--crop_ratio', type=float, default=0.3, help='图像截取起始位置比例 (0.0-1.0, 默认: 0.3表示从30%位置开始截取下70%)')
    parser.add_argument('--use_otsu', action='store_true', default=True, help='使用大津法自动阈值 (默认: True)')
    parser.add_argument('--manual_threshold', action='store_true', help='使用手动阈值而不是大津法')
    parser.add_argument('--use_skeleton', action='store_true', default=True, help='使用骨架化将胶带简化为中心线 (默认: True)')
    parser.add_argument('--no_skeleton', action='store_true', help='不使用骨架化，保持原始胶带宽度')
    parser.add_argument('--enable_stop_detection', action='store_true', default=True, help='启用横向停车线检测 (默认: True)')
    parser.add_argument('--disable_stop_detection', action='store_true', help='禁用横向停车线检测')
    parser.add_argument('--multicore_display', action='store_true', default=True, help='使用多核处理显示 (默认: True)')
    parser.add_argument('--display', action='store_true', default=True, help='显示视觉反馈')
    args = parser.parse_args()

    line_following_node = None

    try:
        # 初始化ROS节点
        rospy.init_node('line_following', anonymous=True)

        # 创建循迹节点
        line_following_node = LineFollowingNode(args)

        # 等待一下让订阅者初始化
        rospy.sleep(1)

        # 运行循迹节点
        line_following_node.run()

    except rospy.ROSInterruptException:
        print("\nROS被中断")
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("正在清理资源...")

        # 确保停止小车
        try:
            twist = Twist()
            twist.linear.x = 0.0
            twist.angular.z = 0.0
            cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
            cmd_vel_pub.publish(twist)
            rospy.sleep(0.1)
        except:
            pass

        # 清理显示进程
        if line_following_node and hasattr(line_following_node, 'display_process') and line_following_node.display_process:
            try:
                if line_following_node.stop_flag:
                    line_following_node.stop_flag.value = 1
                line_following_node.display_process.terminate()
                line_following_node.display_process.join(timeout=1)
                if line_following_node.display_process.is_alive():
                    line_following_node.display_process.kill()
            except:
                pass

        print("资源清理完成，程序退出")

        # 强制退出
        import os
        os._exit(0)

if __name__ == "__main__":
    main()
