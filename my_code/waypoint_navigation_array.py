#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import actionlib
from move_base_msgs.msg import MoveBaseAction, MoveBaseGoal
from geometry_msgs.msg import PoseWithCovarianceStamped, PoseStamped, Twist
from std_msgs.msg import String
from tf.transformations import quaternion_from_euler
import math
import time
import os
import sys
import cv2
import numpy as np
from cv_bridge import CvBridge
from sensor_msgs.msg import Image
from pyzbar import pyzbar
from tf import TransformListener
import traceback
import threading
import multiprocessing

# 设置PyTorch多线程环境变量（必须在import torch之前设置）
num_cores = multiprocessing.cpu_count()
num_workers = max(2, int(num_cores * 0.8))
os.environ["OMP_NUM_THREADS"] = str(num_workers)
os.environ["MKL_NUM_THREADS"] = str(num_workers)

# PID控制器类
class PIDController:
    """简单的PID控制器"""
    def __init__(self, kp=0.01, ki=0.00, kd=0.00, setpoint=0):
        self.kp = kp  # 比例增益
        self.ki = ki  # 积分增益
        self.kd = kd  # 微分增益
        self.setpoint = setpoint  # 设定值
        self.error_sum = 0  # 误差累积
        self.last_error = 0  # 上一次误差
        self.last_time = time.time()  # 上一次时间
        self.output_limits = (-100, 100)  # 输出限制

    def compute(self, process_variable):
        """计算PID输出"""
        # 计算时间差
        current_time = time.time()
        dt = current_time - self.last_time
        if dt <= 0:
            dt = 0.1  # 防止除零错误

        # 计算误差
        error = self.setpoint - process_variable

        # 计算比例项
        p_term = self.kp * error

        # 计算积分项
        self.error_sum += error * dt
        i_term = self.ki * self.error_sum

        # 计算微分项
        d_term = 0
        if dt > 0:
            d_term = self.kd * (error - self.last_error) / dt

        # 计算总输出
        output = p_term + i_term + d_term

        # 限制输出范围
        if output > self.output_limits[1]:
            output = self.output_limits[1]
        elif output < self.output_limits[0]:
            output = self.output_limits[0]

        # 更新状态
        self.last_error = error
        self.last_time = current_time

        return output, (p_term, i_term, d_term)

# 导入YOLO
try:
    from ultralytics import YOLO
    print("成功导入ultralytics库")
except ImportError:
    print("警告: 未安装ultralytics库，请使用以下命令安装:")
    print("pip install ultralytics")

# 添加更多的调试输出
print("脚本开始执行...")
print("Python版本: {}".format(sys.version))
print("OpenCV版本: {}".format(cv2.__version__))
print("当前工作目录: {}".format(os.getcwd()))

class WaypointNavigation:

    def __init__(self):
        rospy.init_node('waypoint_navigation', anonymous=True)

        # 初始化变量
        self.client = None
        self.init_pose_pub = None
        self.current_waypoint = 0
        self.total_waypoints = 0
        self.waypoints = []
        self.is_navigating = False
        self.bridge = CvBridge()
        self.image_sub = None
        self.qr_result = None
        self.is_scanning_qr = False

        # 红绿灯状态控制
        self.traffic_light_1_is_red = False  # 红绿灯1是否为红灯

        # 白线检测相关变量
        self.is_detecting_white_line = False
        self.white_line_detected = False
        self.calibrated_image_sub = None
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
        self.move_forward_distance = 0.0  # 向前移动的距离（米）

        # 创建action client
        rospy.loginfo("创建action client...")
        try:
            # 尝试连接到不同的move_base服务器名称
            server_names = ['move_base', '/move_base']
            connected = False

            for name in server_names:
                try:
                    self.client = actionlib.SimpleActionClient(name, MoveBaseAction)
                    rospy.loginfo("尝试连接到 %s 服务器..." % name)
                    # 设置超时时间为5秒
                    if self.client.wait_for_server(rospy.Duration(5.0)):
                        rospy.loginfo("已连接到 %s 服务器" % name)
                        connected = True
                        break
                except Exception as e:
                    rospy.logwarn("连接到 %s 服务器失败: %s" % (name, str(e)))

            if not connected:
                rospy.logerr("无法连接到任何move_base服务器")
                # 不要提前返回，继续初始化其他变量
        except Exception as e:
            rospy.logerr("连接move_base服务器时出错: %s" % str(e))

        # 初始位置发布者
        self.init_pose_pub = rospy.Publisher('/initialpose', PoseWithCovarianceStamped, queue_size=1)

        # 当前位置和状态
        self.current_waypoint = 0
        self.total_waypoints = 0
        self.waypoints = []
        self.is_navigating = False

        # 二维码识别相关
        self.bridge = CvBridge()
        self.image_sub = None  # 将在需要时初始化
        self.qr_result = None
        self.is_scanning_qr = False

        # 定义导航点 - 直接使用数组
        # 格式: [x, y, yaw(弧度), actions, delay, time]
        # actions: 可以是单个动作字符串，也可以是动作列表，按顺序执行
        # delay: 执行完动作后的延迟时间（秒）
        # time: 导航到该点的最大时间（秒），超过这个时间就直接前往下一个点
        self.waypoints_array = [
            # 您提供的坐标点
            # 红绿灯测试
            [-12.6, 1.45, -0.682, ["detect_white_line", "scan_qrcode"], 0, 30],  # 点1，到达后先检测白线，再扫描二维码，最多等待30秒
            [-14.1, -0.8, -0.682, ["rotate_360_detect_banana"], 0, 60],  # 点2，原地旋转360°并检测香蕉，最多等待60秒


            [-15.922, -0.447,-2.2,"detect_traffic_light",0,30], #红绿灯1
            [-16.884, 0.516,-2.2,"detect_traffic_light",0,30], #红绿灯2
            [-15.400, -0.100,1.098,"",0,30],#路口1
            [-16.891, 1.113,0.550,"",0,30], #路口2


            # [-14.136, -0.902,-2.2,"detect_white_line",0,30],
            # 红绿灯测试
            # [-15.922, -0.147,-2.2,"detect_traffic_light",0,30],
            # [-16.884, 0.516,-2.2,"detect_traffic_light",0,30],

        ]

        # 将数组转换为字典列表
        self.load_waypoints_from_array()

        rospy.loginfo("导航系统初始化完成")

    def load_waypoints_from_array(self):
        """从数组加载导航点"""
        self.waypoints = []

        for point in self.waypoints_array:
            x, y, yaw = point[0], point[1], point[2]
            actions = point[3] if len(point) > 3 else ""
            delay = point[4] if len(point) > 4 else 0
            time = point[5] if len(point) > 5 else 60  # 默认60秒

            # 确保actions是列表形式
            if isinstance(actions, str):
                if actions:  # 如果是非空字符串
                    actions = [actions]  # 转换为单元素列表
                else:
                    actions = []  # 空字符串转换为空列表

            self.waypoints.append({
                'x': x,
                'y': y,
                'yaw': yaw,
                'actions': actions,
                'delay': delay,
                'time': time
            })

        self.total_waypoints = len(self.waypoints)
        self.current_waypoint = 0

        rospy.loginfo("成功加载 %d 个导航点" % self.total_waypoints)
        return True

    def set_initial_pose(self, x, y, yaw):
        """设置初始位置"""
        init_pose = PoseWithCovarianceStamped()
        init_pose.header.stamp = rospy.Time.now()
        init_pose.header.frame_id = "map"

        init_pose.pose.pose.position.x = x
        init_pose.pose.pose.position.y = y
        init_pose.pose.pose.position.z = 0.0

        # 将偏航角转换为四元数
        q = quaternion_from_euler(0, 0, yaw)
        init_pose.pose.pose.orientation.x = q[0]
        init_pose.pose.pose.orientation.y = q[1]
        init_pose.pose.pose.orientation.z = q[2]
        init_pose.pose.pose.orientation.w = q[3]

        # 设置协方差
        init_pose.pose.covariance = [0.25, 0.0, 0.0, 0.0, 0.0, 0.0,
                                    0.0, 0.25, 0.0, 0.0, 0.0, 0.0,
                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.06853891945200942]

        # 发布初始位置
        rospy.loginfo("设置初始位置: x=%f, y=%f, yaw=%f" % (x, y, yaw))
        self.init_pose_pub.publish(init_pose)
        rospy.sleep(1)  # 等待位置更新

    def send_goal(self, x, y, yaw):
        """发送导航目标"""
        goal = MoveBaseGoal()
        goal.target_pose.header.frame_id = "map"
        goal.target_pose.header.stamp = rospy.Time.now()

        goal.target_pose.pose.position.x = x
        goal.target_pose.pose.position.y = y
        goal.target_pose.pose.position.z = 0.0

        # 将偏航角转换为四元数
        q = quaternion_from_euler(0, 0, yaw)
        goal.target_pose.pose.orientation.x = q[0]
        goal.target_pose.pose.orientation.y = q[1]
        goal.target_pose.pose.orientation.z = q[2]
        goal.target_pose.pose.orientation.w = q[3]

        rospy.loginfo("导航到: x=%f, y=%f, yaw=%f" % (x, y, yaw))
        self.client.send_goal(goal)

        return goal

    def navigate_to_waypoint(self, index):
        """导航到指定索引的导航点"""
        if index < 0 or index >= self.total_waypoints:
            rospy.logerr("导航点索引超出范围: %d" % index)
            return False

        # 检查move_base服务器是否可用
        if self.client is None:
            rospy.logerr("move_base客户端未初始化")
            return False

        waypoint = self.waypoints[index]
        self.current_waypoint = index

        try:
            # 发送导航目标
            self.send_goal(waypoint['x'], waypoint['y'], waypoint['yaw'])

            # 等待导航完成，使用指定的最大时间
            self.is_navigating = True
            max_time = waypoint['time']  # 使用指定的最大时间
            rospy.loginfo("导航到点 %d，最大时间: %d 秒" % (index+1, max_time))

            # 使用更宽松的判断条件，在指定时间内完成导航
            result = self.client.wait_for_result(rospy.Duration(max_time))
            self.is_navigating = False

            # 无论是否到达目标点，都认为导航成功
            rospy.loginfo("导航点 %d 导航完成" % (index+1))
            if not result:
                rospy.logwarn("导航点 %d 超时，将继续前往下一个点" % (index+1))
                # 取消当前导航目标
                self.client.cancel_goal()

            # 检查是否需要在此位置播放音频
            if hasattr(self, 'should_play_audio_at_destination') and self.should_play_audio_at_destination:
                rospy.loginfo("到达目标位置，播放音频")
                print("\n" + "="*50)
                print("【目标位置音频】: 到达目标位置，播放音频")
                print("="*50 + "\n")

                # 播放音频
                self.play_audio_file(self.destination_audio_file, self.destination_audio_description)

                # 重置音频播放标志
                self.should_play_audio_at_destination = False
                self.destination_audio_file = ""
                self.destination_audio_description = ""

            # 检查是否到达路口1或路口2，如果是则运行线跟踪脚本
            if index == 4 or index == 5:  # 索引4是路口1，索引5是路口2
                intersection_name = "路口1" if index == 4 else "路口2"
                rospy.loginfo("到达%s，启动线跟踪程序" % intersection_name)
                print("\n" + "="*50)
                print("【线跟踪启动】: 到达%s，启动线跟踪程序" % intersection_name)
                print("="*50 + "\n")

                # 运行线跟踪脚本
                self.run_line_following_script(intersection_name)

            # 执行额外的动作（如果有）
            if waypoint['actions'] and len(waypoint['actions']) > 0:
                rospy.loginfo("开始执行 %d 个任务..." % len(waypoint['actions']))
                for i, action in enumerate(waypoint['actions']):
                    rospy.loginfo("执行任务 %d/%d: %s" % (i+1, len(waypoint['actions']), action))
                    self.execute_action(action)
                rospy.loginfo("所有任务执行完成")

            # 等待指定的延迟时间
            if waypoint['delay'] > 0:
                rospy.loginfo("等待 %d 秒" % waypoint['delay'])
                rospy.sleep(waypoint['delay'])

            return True
        except Exception as e:
            rospy.logerr("导航到导航点 %d 时出错: %s" % (index+1, str(e)))
            self.is_navigating = False
            return False

    def navigate_all_waypoints(self):
        """根据红绿灯状态导航所有导航点"""
        if self.total_waypoints == 0:
            rospy.logerr("没有导航点可导航")
            return False

        rospy.loginfo("开始导航所有 %d 个导航点" % self.total_waypoints)

        # 初始化next_waypoint属性和完成标志
        self.next_waypoint = 0
        self.navigation_complete_after_next = False

        # 最大循环次数，防止无限循环
        max_iterations = 10
        iteration_count = 0

        success = True
        while iteration_count < max_iterations:
            iteration_count += 1

            # 获取当前要导航的点
            i = self.next_waypoint

            # 检查是否已经完成所有导航点
            if i >= self.total_waypoints:
                rospy.loginfo("已完成所有导航点")
                break

            rospy.loginfo(f"导航到点 {i+1}/{self.total_waypoints} (索引: {i})")

            # 保存当前导航点索引，用于检测是否被修改
            current_next_waypoint = self.next_waypoint

            # 导航到当前点
            if not self.navigate_to_waypoint(i):
                success = False
                break

            # 检查是否设置了完成标志
            should_end_after_next = False
            if hasattr(self, 'navigation_complete_after_next') and self.navigation_complete_after_next:
                should_end_after_next = True
                rospy.loginfo("检测到完成标志，将在下一个导航点后结束导航")
                print("\n" + "="*50)
                print("【导航状态】: 将在下一个导航点后结束导航")
                print("="*50 + "\n")

            # 如果当前点是最后一个点，结束导航
            if i == self.total_waypoints - 1:
                rospy.loginfo("已到达最后一个导航点")
                break

            # 检查next_waypoint是否被修改（例如被红绿灯检测函数修改）
            if self.next_waypoint == current_next_waypoint:
                # 如果没有被修改，默认导航到下一个点
                self.next_waypoint = i + 1
                rospy.loginfo(f"默认设置下一个导航点为: {self.next_waypoint}")
            else:
                # 如果被修改，使用修改后的值
                rospy.loginfo(f"检测到next_waypoint被修改为: {self.next_waypoint}")

            # 如果设置了完成标志，并且已经导航到了当前点，那么导航到下一个点后结束
            if should_end_after_next:
                # 保存下一个导航点索引
                next_point = self.next_waypoint

                # 导航到下一个点
                rospy.loginfo(f"导航到最终点 {next_point+1}/{self.total_waypoints} (索引: {next_point})")
                if not self.navigate_to_waypoint(next_point):
                    success = False
                else:
                    rospy.loginfo("已到达最终导航点，导航结束")
                    print("\n" + "="*50)
                    print("【导航完成】: 根据红绿灯状态，导航任务结束")
                    print("="*50 + "\n")

                # 导航结束
                break

            print(f"\n{'='*50}")
            print(f"【导航状态】: 当前点 {i+1}/{self.total_waypoints} (索引: {i})")
            print(f"【下一个点】: {self.next_waypoint+1}/{self.total_waypoints} (索引: {self.next_waypoint})")
            print(f"【迭代计数】: {iteration_count}/{max_iterations}")
            print(f"【完成标志】: {'是' if hasattr(self, 'navigation_complete_after_next') and self.navigation_complete_after_next else '否'}")
            print(f"{'='*50}\n")

        if success:
            rospy.loginfo("所有导航点导航完成")
        else:
            rospy.logerr("导航在导航点 %d 处失败" % (self.current_waypoint+1))

        return success

    def execute_action(self, action):
        """执行额外的动作"""
        rospy.loginfo("执行动作: %s" % action)

        # 这里可以根据action字符串执行不同的动作
        # 例如: 拍照、抓取物体、播放声音等

        # 示例: 简单的延迟
        if action == "wait":
            rospy.sleep(2)

        # 示例: 打印消息
        elif action == "speak":
            rospy.loginfo("我到达了目标点!")

        # 在这里添加更多的动作...

        # 自定义动作1: 拍照
        elif action == "take_photo":
            self.take_photo()

        # 自定义动作2: 播放声音
        elif action == "play_sound":
            self.play_sound()

        # 自定义动作3: 扫描二维码
        elif action == "scan_qrcode":
            self.scan_qrcode()

        # 自定义动作4: 检测白线并向前移动
        elif action == "detect_white_line":
            self.detect_white_line()
            # self.scan_qrcode()

        # 自定义动作5: 原地旋转360°并检测香蕉
        elif action == "rotate_360_detect_banana":
            self.rotate_360_detect_banana()

        # 自定义动作6: 检测红绿灯
        elif action == "detect_traffic_light":
            # 检查当前是否在红绿灯2位置且红绿灯1是红灯
            if self.current_waypoint == 3 and self.traffic_light_1_is_red:  # 索引3是红绿灯2
                # 红绿灯1是红灯时，红绿灯2位置不检测，直接播放音频并导航到路口2
                rospy.loginfo("红绿灯1是红灯，红绿灯2位置跳过检测，直接播放路口2音频并导航到路口2")
                print("\n" + "="*50)
                print("【红绿灯2逻辑】: 红绿灯1是红灯，跳过红绿灯2检测")
                print("【决策】: 直接播放路口2音频并导航到路口2")
                print("="*50 + "\n")

                # 播放路口2音频
                self.play_audio_file("/home/<USER>/ucar_ws/my_code/voice/路口2.wav", "路口2")

                # 设置下一个导航点为路口2
                self.next_waypoint = 5  # 索引5对应路口2
                self.navigation_complete_after_next = True  # 设置完成标志

                print("\n" + "="*50)
                print(f"【导航设置】: 下一个导航点 = {self.next_waypoint} (路口2)")
                print(f"【完成标志】: {'是' if self.navigation_complete_after_next else '否'}")
                print("="*50 + "\n")
            else:
                # 正常的红绿灯检测逻辑
                result = self.detect_traffic_light()
                rospy.loginfo(f"红绿灯检测结果: {result}")
                print("\n" + "="*50)
                print(f"【红绿灯检测】: 检测结果 = {result}")
                print(f"【下一个导航点】: {self.next_waypoint}")

                # 检查是否设置了完成标志
                if hasattr(self, 'navigation_complete_after_next'):
                    print(f"【完成标志】: {'是' if self.navigation_complete_after_next else '否'}")
                else:
                    print("【完成标志】: 未设置")

                print("="*50 + "\n")

        # 未知动作
        else:
            rospy.logwarn("未知动作: %s" % action)

    # 以下是可以扩展的功能方法

    def take_photo(self):
        """拍照功能"""
        rospy.loginfo("拍照...")
        # 这里添加拍照的代码
        # 例如: 调用相机节点拍照并保存
        try:
            # 示例代码，需要根据实际相机进行修改
            rospy.loginfo("拍照完成")
        except Exception as e:
            rospy.logerr("拍照失败: %s" % str(e))

    def play_sound(self):
        """播放声音功能"""
        rospy.loginfo("播放声音...")
        # 这里添加播放声音的代码
        try:
            # 示例代码，需要根据实际声音设备进行修改
            os.system("aplay /path/to/sound.wav")
            rospy.loginfo("播放声音完成")
        except Exception as e:
            rospy.logerr("播放声音失败: %s" % str(e))

    def scan_qrcode(self):
        """二维码识别功能"""
        rospy.loginfo("开始扫描二维码...")

        # 设置扫描状态
        self.is_scanning_qr = True
        self.qr_result = None

        # 订阅相机图像话题
        self.image_sub = rospy.Subscriber('/usb_cam/image_raw', Image, self.image_callback)

        # 等待扫描结果，最多等待10秒
        start_time = rospy.Time.now()
        timeout = rospy.Duration(10.0)  # 10秒超时
        rate = rospy.Rate(3)  # 3Hz

        while self.is_scanning_qr and (rospy.Time.now() - start_time) < timeout:
            if self.qr_result:
                rospy.loginfo("扫描到二维码: %s" % self.qr_result)
                break
            rate.sleep()

        # 取消订阅
        if self.image_sub:
            self.image_sub.unregister()
            self.image_sub = None

        # 检查结果并播放相应音频
        if self.qr_result:
            self.play_audio_based_on_qr_result(self.qr_result)
        else:
            rospy.logwarn("未扫描到二维码或超时")

        # 重置扫描状态
        self.is_scanning_qr = False

    def play_audio_based_on_qr_result(self, qr_content):
        """根据二维码内容播放相应的音频文件"""
        rospy.loginfo("根据二维码内容播放音频: %s" % qr_content)

        # 定义音频文件路径映射
        audio_files = {
            "Fruit": "/home/<USER>/ucar_ws/my_code/voice/1_Fruit.wav",
            "Dessert": "/home/<USER>/ucar_ws/my_code/voice/1_Dessert.wav",
            "Vegetable": "/home/<USER>/ucar_ws/my_code/voice/1_Vegetable.wav"
        }

        # 清理二维码内容（去除空格和换行符）
        qr_content = qr_content.strip()

        # 检查是否有对应的音频文件
        if qr_content in audio_files:
            audio_file = audio_files[qr_content]

            # 检查音频文件是否存在
            if os.path.exists(audio_file):
                rospy.loginfo("播放音频文件: %s" % audio_file)
                print(f"【音频播放】: 检测到 {qr_content}，播放音频文件: {audio_file}")

                try:
                    # 使用aplay播放WAV文件
                    os.system("aplay %s" % audio_file)
                    rospy.loginfo("音频播放完成")
                    print(f"【音频播放】: {qr_content} 音频播放完成")
                except Exception as e:
                    rospy.logerr("播放音频失败: %s" % str(e))
                    print(f"【音频播放】: 播放失败 - {str(e)}")
            else:
                rospy.logwarn("音频文件不存在: %s" % audio_file)
                print(f"【音频播放】: 音频文件不存在 - {audio_file}")
        else:
            rospy.logwarn("未识别的二维码内容，无对应音频文件: %s" % qr_content)
            print(f"【音频播放】: 未识别的二维码内容 - {qr_content}")
            print(f"【音频播放】: 支持的内容: {list(audio_files.keys())}")

    def play_audio_file(self, audio_file_path, description="音频"):
        """播放指定的音频文件"""
        rospy.loginfo("播放%s音频: %s" % (description, audio_file_path))
        print(f"【音频播放】: 播放{description}音频: {audio_file_path}")

        # 检查音频文件是否存在
        if os.path.exists(audio_file_path):
            try:
                # 使用aplay播放WAV文件
                result = os.system("aplay %s" % audio_file_path)
                if result == 0:
                    rospy.loginfo("%s音频播放完成" % description)
                    print(f"【音频播放】: {description}音频播放完成")
                else:
                    rospy.logerr("%s音频播放失败，返回码: %d" % (description, result))
                    print(f"【音频播放】: {description}音频播放失败，返回码: {result}")
            except Exception as e:
                rospy.logerr("播放%s音频失败: %s" % (description, str(e)))
                print(f"【音频播放】: 播放{description}音频失败 - {str(e)}")
        else:
            rospy.logwarn("%s音频文件不存在: %s" % (description, audio_file_path))
            print(f"【音频播放】: {description}音频文件不存在 - {audio_file_path}")

    def run_line_following_script(self, intersection_name):
        """运行线跟踪脚本"""
        rospy.loginfo("在%s启动线跟踪程序" % intersection_name)
        print(f"【线跟踪】: 在{intersection_name}启动线跟踪程序")

        # 线跟踪脚本路径
        script_path = "/home/<USER>/ucar_ws/my_code/run_line_following.sh"

        # 检查脚本是否存在
        if os.path.exists(script_path):
            try:
                rospy.loginfo("执行线跟踪脚本: %s" % script_path)
                print(f"【线跟踪】: 执行脚本: {script_path}")

                # 使用subprocess运行脚本，这样可以更好地控制进程
                import subprocess

                # 运行脚本
                print(f"【线跟踪】: 正在启动线跟踪程序...")
                print(f"【线跟踪】: 脚本将在后台运行")
                print(f"【线跟踪】: 如需停止，请手动终止线跟踪程序")

                # 在后台运行脚本
                process = subprocess.Popen([script_path],
                                         shell=True,
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE)

                rospy.loginfo("线跟踪脚本已启动，PID: %d" % process.pid)
                print(f"【线跟踪】: 脚本已启动，进程ID: {process.pid}")
                print(f"【线跟踪】: 导航任务完成，线跟踪程序接管控制")

                # 等待一小段时间确保脚本启动
                rospy.sleep(2)

                # 检查进程是否还在运行
                if process.poll() is None:
                    print(f"【线跟踪】: 线跟踪程序运行正常")
                else:
                    print(f"【线跟踪】: 警告 - 线跟踪程序可能已退出")

            except Exception as e:
                rospy.logerr("启动线跟踪脚本失败: %s" % str(e))
                print(f"【线跟踪】: 启动失败 - {str(e)}")
        else:
            rospy.logwarn("线跟踪脚本不存在: %s" % script_path)
            print(f"【线跟踪】: 脚本文件不存在 - {script_path}")

    def image_callback(self, msg):
        """相机图像回调函数，用于二维码识别"""
        if not self.is_scanning_qr:
            return

        try:
            # 将ROS图像转换为OpenCV格式
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")

            # 使用pyzbar进行二维码识别
            barcodes = pyzbar.decode(cv_image)

            # 处理识别结果
            for barcode in barcodes:
                # 提取二维码数据
                barcode_data = barcode.data.decode("utf-8")
                barcode_type = barcode.type

                # 在图像上标记二维码
                (x, y, w, h) = barcode.rect
                cv2.rectangle(cv_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

                # 显示二维码类型和数据
                text = "%s: %s" % (barcode_type, barcode_data)
                cv2.putText(cv_image, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                # 保存结果
                self.qr_result = barcode_data
                self.is_scanning_qr = False  # 停止扫描

                rospy.loginfo("识别到%s: %s" % (barcode_type, barcode_data))

            # 显示图像（可选）
            # cv2.imshow("QR Code Scanner", cv_image)
            # cv2.waitKey(1)

        except Exception as e:
            rospy.logerr("处理图像时出错: %s" % str(e))

    def calibrated_image_callback(self, msg):
        """校正后的相机图像回调函数，用于白线检测"""
        if not self.is_detecting_white_line:
            return

        try:
            # 将ROS图像转换为NumPy数组
            img_data = np.frombuffer(msg.data, dtype=np.uint8).reshape(msg.height, msg.width, -1)

            # 转换为灰度图
            gray = cv2.cvtColor(img_data, cv2.COLOR_BGR2GRAY)

            # 使用自适应阈值处理，更好地检测细线
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                          cv2.THRESH_BINARY, 11, 2)

            # 获取图像下方区域（假设白线在图像下方）
            height, width = binary.shape
            bottom_region = binary[int(height*0.5):int(height*0.95), :]  # 只关注下方20%的区域

            # 使用形态学操作增强横线
            kernel = np.ones((1, 15), np.uint8)  # 横向kernel，增强横线
            enhanced = cv2.morphologyEx(bottom_region, cv2.MORPH_CLOSE, kernel)

            # 使用霍夫变换检测直线
            edges = cv2.Canny(enhanced, 50, 150, apertureSize=3)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                                   minLineLength=width*0.3, maxLineGap=20)

            # 判断是否检测到横线
            has_horizontal_line = False
            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    # 计算线的角度
                    angle = np.abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)
                    # 如果角度接近0或180度，认为是横线
                    if angle < 40 or angle > 140:
                        has_horizontal_line = True
                        # 在原图上绘制检测到的线（用于调试）
                        # cv2.line(img_data, (x1, y1 + int(height*0.7)), (x2, y2 + int(height*0.7)), (0, 0, 255), 2)
                        rospy.loginfo("检测到横线，角度: %.2f度", angle)
                        break

            # 计算下方区域中白色像素的比例（作为辅助判断）
            white_pixel_ratio = np.sum(bottom_region == 255) / (bottom_region.shape[0] * bottom_region.shape[1])

            # 显示处理后的图像（可选）
            # cv2.imshow("White Line Detection", enhanced)
            # cv2.waitKey(1)

            # 添加连续未检测计数器（如果不存在）
            if not hasattr(self, 'consecutive_no_detection'):
                self.consecutive_no_detection = 0

            # 判断是否检测到白线（优先使用霍夫变换的结果）
            if has_horizontal_line:
                rospy.loginfo("通过霍夫变换检测到横线")
                self.white_line_detected = True
                self.consecutive_no_detection = 0  # 重置计数器
            # elif white_pixel_ratio > 0.1:  # 如果白色像素比例大于10%，作为备选判断
            #     rospy.loginfo("通过像素比例检测到白线: %.2f%%", white_pixel_ratio * 100)
            #     self.white_line_detected = True
            #     self.consecutive_no_detection = 0  # 重置计数器
            else:
                rospy.loginfo("未检测到白线，白色像素比例: %.2f%% (连续未检测次数: %d/5)",
                             white_pixel_ratio * 100, self.consecutive_no_detection + 1)

                # 增加连续未检测计数
                self.consecutive_no_detection += 1

                # 只有连续5次未检测到白线，才将white_line_detected设置为False
                if self.consecutive_no_detection >= 5:
                    rospy.loginfo("已连续5次未检测到白线，确认没有白线")
                    self.white_line_detected = False
                    self.consecutive_no_detection = 0  # 重置计数器

        except Exception as e:
            rospy.logerr("处理图像时出错: %s" % str(e))

    def detect_white_line(self):
        """白线检测功能"""
        rospy.loginfo("开始检测横向白线...")

        # 设置检测状态
        self.is_detecting_white_line = True
        self.white_line_detected = False

        # 订阅校正后的相机图像话题
        self.calibrated_image_sub = rospy.Subscriber('/usb_cam/image_calibrated', Image, self.calibrated_image_callback)

        # 等待检测结果，最多等待8秒
        start_time = rospy.Time.now()
        timeout = rospy.Duration(8.0)  # 8秒超时
        rate = rospy.Rate(10)  # 10Hz

        # 检测计数器
        detection_count = 0
        positive_count = 0

        # 进行多次检测，提高准确性
        while (rospy.Time.now() - start_time) < timeout and detection_count < 5:
            detection_count += 1
            if self.white_line_detected:
                positive_count += 1
            rospy.loginfo("检测 %d/5: %s", detection_count, "检测到白线" if self.white_line_detected else "未检测到白线")
            rate.sleep()

        # 取消订阅
        if self.calibrated_image_sub:
            self.calibrated_image_sub.unregister()
            self.calibrated_image_sub = None

        # 重置检测状态
        self.is_detecting_white_line = False

        # 根据检测结果控制小车向前移动
        if positive_count >= 2:  # 如果至少有2次检测到白线，认为存在白线
            rospy.loginfo("检测到横向白线 (%d/%d)，小车向前移动直到没有白线...", positive_count, detection_count)
            self.white_line_detected = True
            self.move_forward_until_no_white_line()
        else:
            rospy.loginfo("未检测到横向白线 (%d/%d)，继续执行下一个任务", positive_count, detection_count)

    def move_forward_until_no_white_line(self):
        """控制小车向前移动直到没有白线"""
        # 设置检测状态
        self.is_detecting_white_line = True
        self.white_line_detected = True

        # 订阅校正后的相机图像话题
        self.calibrated_image_sub = rospy.Subscriber('/usb_cam/image_calibrated', Image, self.calibrated_image_callback)

        # 等待一段时间，确保获取到最新的图像
        rospy.sleep(1.0)

        # 如果没有检测到白线，不需要移动
        if not self.white_line_detected:
            rospy.loginfo("未检测到白线，不需要移动")

            # 取消订阅
            if self.calibrated_image_sub:
                self.calibrated_image_sub.unregister()
                self.calibrated_image_sub = None

            # 重置检测状态
            self.is_detecting_white_line = False
            return

        rospy.loginfo("检测到白线，开始向前移动...")

        # 创建速度消息
        cmd_vel = Twist()
        cmd_vel.linear.x = 0.05  # 设置线速度为0.05 m/s，更慢更精确

        # 设置超时时间，防止无限移动
        start_time = rospy.Time.now()
        timeout = rospy.Duration(20.0)  # 20秒超时
        rate = rospy.Rate(10)  # 10Hz

        # 记录起始位置
        self.move_forward_distance = 0.0
        last_time = rospy.Time.now()

        # 向前移动直到没有白线或超时
        consecutive_no_line = 0  # 连续几次没有检测到白线

        while (rospy.Time.now() - start_time) < timeout:
            # 发布速度命令
            self.cmd_vel_pub.publish(cmd_vel)

            # 计算移动距离
            current_time = rospy.Time.now()
            dt = (current_time - last_time).to_sec()
            self.move_forward_distance += cmd_vel.linear.x * dt
            last_time = current_time

            # 显示移动距离
            if int(self.move_forward_distance * 100) % 5 == 0:  # 每移动5cm显示一次
                rospy.loginfo("已向前移动: %.2f 米", self.move_forward_distance)

            # 检查是否已经越过白线
            if not self.white_line_detected:
                consecutive_no_line += 1
                rospy.loginfo("未检测到白线 (%d/3)", consecutive_no_line)
                if consecutive_no_line >= 3:  # 连续3次未检测到白线，认为已经越过
                    rospy.loginfo("已连续3次未检测到白线，认为已越过白线")
                    break
            else:
                consecutive_no_line = 0  # 重置计数器
                rospy.loginfo("仍然检测到白线")

            rate.sleep()

        # 再向前移动一小段距离，确保完全越过白线
        extra_distance = 0.05  # 额外移动10cm
        extra_start_time = rospy.Time.now()

        rospy.loginfo("额外向前移动 %.2f 米...", extra_distance)

        while (rospy.Time.now() - extra_start_time).to_sec() < (extra_distance / cmd_vel.linear.x):
            self.cmd_vel_pub.publish(cmd_vel)
            rate.sleep()

        # 停止小车
        cmd_vel.linear.x = 0.0
        self.cmd_vel_pub.publish(cmd_vel)

        # 取消订阅
        if self.calibrated_image_sub:
            self.calibrated_image_sub.unregister()
            self.calibrated_image_sub = None

        # 重置检测状态
        self.is_detecting_white_line = False

        # 显示结果
        if (rospy.Time.now() - start_time) >= timeout:
            rospy.loginfo("超时停止，总共向前移动: %.2f 米", self.move_forward_distance + extra_distance)
        else:
            rospy.loginfo("已移动到没有白线的位置，总共向前移动: %.2f 米", self.move_forward_distance + extra_distance)



    def rotate_360_detect_banana(self):
        """原地旋转360°并检测香蕉，检测到后停止旋转"""
        rospy.loginfo("准备开始香蕉检测...")

        # 创建速度消息
        cmd_vel = Twist()
        cmd_vel.angular.z = 0.6  # 增加角速度为0.6 rad/s，大约需要10秒完成一圈

        # 设置超时时间
        timeout = rospy.Duration(60.0)  # 60秒超时
        rate = rospy.Rate(10)  # 10Hz

        # 香蕉检测标志
        banana_detected = False

        try:
            # 加载YOLO模型
            rospy.loginfo("加载YOLO模型...")
            print("正在加载目标检测模型，请稍候...")

            # 尝试多个可能的模型路径
            model_paths = [
                '/home/<USER>/ucar_ws/my_code/best.pt',  # 主要路径
                '/home/<USER>/ucar_ws/best.pt',          # 备用路径1
                '/home/<USER>/best.pt',                  # 备用路径2
                # '/home/<USER>/ucar_ws/my_code/yolov8n.pt'  # 默认模型
            ]

            model_path = None
            for path in model_paths:
                if os.path.exists(path):
                    model_path = path
                    rospy.loginfo("找到模型文件: %s", model_path)
                    print(f"找到模型文件: {model_path}")
                    break

            # 检查模型文件是否存在
            if model_path is None:
                rospy.logerr("未找到任何模型文件，尝试过以下路径: %s", model_paths)
                print("错误: 未找到任何模型文件!")
                return False

            try:
                # 加载模型
                rospy.loginfo("开始加载模型: %s", model_path)
                print(f"开始加载模型: {model_path}")
                model = YOLO(model_path)

                # 配置模型参数
                model.conf = 0.8  # 置信度阈值 (提高到0.8)
                model.iou = 0.45   # IoU阈值

                # 获取模型支持的类别
                class_names = model.names
                rospy.loginfo("模型支持的类别: %s", class_names)
                print(f"模型支持的类别: {class_names}")

                # 根据二维码内容选择要检测的类别
                target_class_ids = []
                target_class_name = ""

                # 检查是否有二维码扫描结果
                if hasattr(self, 'qr_result') and self.qr_result:
                    qr_content = self.qr_result.strip()
                    rospy.loginfo("根据二维码内容 '%s' 选择检测类别", qr_content)
                    print(f"根据二维码内容 '{qr_content}' 选择检测类别")

                    if qr_content == "Vegetable":
                        # 检测蔬菜类（ID为0、1、2，即辣椒、西红柿、土豆）
                        target_class_ids = [0, 1, 2]
                        target_class_name = "蔬菜"
                    elif qr_content == "Fruit":
                        # 检测水果类（ID为3、4、5，即香蕉、苹果、西瓜）
                        target_class_ids = [3, 4, 5]
                        target_class_name = "水果"
                    elif qr_content == "Dessert":
                        # 检测甜点类（ID为6、7、8，即可乐、蛋糕、牛奶）
                        target_class_ids = [6, 7, 8]
                        target_class_name = "甜点"
                    else:
                        # 如果二维码内容不匹配，默认检测香蕉
                        target_class_ids = [3]  # 默认检测香蕉
                        target_class_name = "香蕉"
                        rospy.logwarn("未识别的二维码内容 '%s'，默认检测香蕉", qr_content)
                        print(f"未识别的二维码内容 '{qr_content}'，默认检测香蕉")
                else:
                    # 如果没有二维码扫描结果，默认检测香蕉
                    target_class_ids = [3]  # 默认检测香蕉
                    target_class_name = "香蕉"
                    rospy.logwarn("没有二维码扫描结果，默认检测香蕉")
                    print("没有二维码扫描结果，默认检测香蕉")

                # 验证类别ID是否在模型支持的范围内
                valid_class_ids = []
                for class_id in target_class_ids:
                    if class_id in class_names:
                        valid_class_ids.append(class_id)
                        rospy.loginfo("将检测类别: ID=%d, 名称=%s", class_id, class_names[class_id])
                        print(f"将检测类别: ID={class_id}, 名称={class_names[class_id]}")
                    else:
                        rospy.logwarn("类别ID %d 不在模型支持的范围内", class_id)
                        print(f"类别ID {class_id} 不在模型支持的范围内")

                if valid_class_ids:
                    model.classes = valid_class_ids  # 设置要检测的类别
                    banana_class_id = valid_class_ids[0]  # 保存第一个有效ID以便后续使用
                    rospy.loginfo("设置模型检测类别: %s", valid_class_ids)
                    print(f"设置模型检测{target_class_name}类别: {valid_class_ids}")
                else:
                    # 如果没有有效的类别ID，使用默认的类别ID 3
                    banana_class_id = 3  # 保存默认ID以便后续使用
                    model.classes = [banana_class_id]
                    rospy.logwarn("未找到有效的类别ID，使用默认类别ID: %d", banana_class_id)
                    print(f"未找到有效的类别ID，使用默认类别ID: {banana_class_id}")

                # 预热模型
                rospy.loginfo("开始预热模型...")
                print("开始预热模型，这可能需要几秒钟...")

                # 创建一个更大的测试图像进行预热
                dummy_img = np.zeros((640, 640, 3), dtype=np.uint8)

                # 多次预热以确保模型完全加载
                for i in range(3):
                    rospy.loginfo(f"预热模型 {i+1}/3...")
                    results = model.predict(dummy_img, verbose=False)
                    rospy.sleep(0.5)  # 短暂暂停确保预热完成

                # 检查预热结果
                if results and len(results) > 0:
                    rospy.loginfo("模型预热成功，结果形状: %s", results[0].boxes.shape)
                    print("模型预热成功!")
                else:
                    rospy.logwarn("模型预热完成，但结果为空")
                    print("模型预热完成，但结果为空")

                rospy.loginfo("模型加载完成!")
                print("模型加载和预热完成，准备开始旋转检测香蕉...")

            except Exception as e:
                rospy.logerr("加载模型时出错: %s", str(e))
                rospy.logerr(traceback.format_exc())
                return False

            # 创建图像订阅者
            self.bridge = CvBridge()
            self.latest_frame = None
            self.frame_received = False

            # 订阅图像话题
            rospy.loginfo("订阅图像话题...")

            # 检查可用的图像话题
            available_topics = rospy.get_published_topics()
            rospy.loginfo("可用的ROS话题: %s", available_topics)

            image_topics = [topic for topic, topic_type in available_topics if topic_type == 'sensor_msgs/Image']
            rospy.loginfo("可用的图像话题: %s", image_topics)

            # 如果没有找到任何图像话题，尝试启动摄像头
            if not image_topics:
                rospy.logwarn("未找到任何图像话题，尝试启动摄像头...")
                try:
                    # 尝试启动usb_cam节点
                    import subprocess
                    subprocess.Popen(["roslaunch", "usb_cam", "usb_cam-test.launch"],
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE)
                    rospy.loginfo("已尝试启动usb_cam节点，等待10秒...")
                    rospy.sleep(10.0)  # 等待摄像头启动

                    # 重新检查可用的图像话题
                    available_topics = rospy.get_published_topics()
                    image_topics = [topic for topic, topic_type in available_topics if topic_type == 'sensor_msgs/Image']
                    rospy.loginfo("启动摄像头后可用的图像话题: %s", image_topics)
                except Exception as e:
                    rospy.logerr("尝试启动摄像头时出错: %s", str(e))

            # 优先使用校正后的图像话题
            if '/usb_cam/image_calibrated' in image_topics:
                image_topic = '/usb_cam/image_calibrated'
                rospy.loginfo("使用校正后的图像话题: %s", image_topic)
            elif '/usb_cam/image_raw' in image_topics:
                image_topic = '/usb_cam/image_raw'
                rospy.loginfo("使用原始图像话题: %s", image_topic)
            elif '/camera/rgb/image_raw' in image_topics:
                image_topic = '/camera/rgb/image_raw'
                rospy.loginfo("使用RGB相机图像话题: %s", image_topic)
            elif '/camera/image_raw' in image_topics:
                image_topic = '/camera/image_raw'
                rospy.loginfo("使用相机图像话题: %s", image_topic)
            else:
                # 如果没有找到预期的话题，使用第一个可用的图像话题
                if image_topics:
                    image_topic = image_topics[0]
                    rospy.loginfo("使用可用的图像话题: %s", image_topic)
                else:
                    rospy.logerr("未找到任何图像话题，无法进行检测")
                    return False

            # 定义图像回调函数
            def image_callback(msg):
                try:
                    # 将ROS图像转换为OpenCV格式
                    frame = self.bridge.imgmsg_to_cv2(msg, "bgr8")

                    # 检查图像是否有效
                    if frame is not None and frame.size > 0:
                        # 检查图像尺寸
                        height, width = frame.shape[:2]
                        if height > 0 and width > 0:
                            self.latest_frame = frame
                            self.frame_received = True
                            # 添加时间戳，用于检查图像是否更新
                            self.latest_frame_time = time.time()
                            # 不再打印每次收到新图像的信息，减少日志输出
                        else:
                            rospy.logwarn("收到无效图像，尺寸: %dx%d", width, height)
                    else:
                        rospy.logwarn("收到空图像")
                except Exception as e:
                    rospy.logerr("处理图像时出错: %s", str(e))
                    rospy.logerr(traceback.format_exc())

            # 订阅图像话题
            image_sub = rospy.Subscriber(image_topic, Image, image_callback)

            # 等待接收第一帧图像
            rospy.loginfo("等待接收图像...")
            wait_start = time.time()

            # 创建一个标志，用于指示是否收到图像
            image_received = False

            # 创建一个计数器，用于记录等待次数
            wait_count = 0

            # 等待最多30秒
            while not image_received and time.time() - wait_start < 30.0:
                # 每隔1秒检查一次
                rospy.sleep(1.0)
                wait_count += 1

                # 检查是否收到图像
                if self.frame_received and self.latest_frame is not None:
                    image_received = True
                    rospy.loginfo("成功接收到图像，尺寸: %dx%d", self.latest_frame.shape[1], self.latest_frame.shape[0])
                    break

                # 每5秒打印一次等待信息
                if wait_count % 5 == 0:
                    rospy.logwarn("等待图像中...已等待 %d 秒", wait_count)

                    # 检查图像话题是否仍然存在
                    available_topics = rospy.get_published_topics()
                    image_topics = [topic for topic, topic_type in available_topics if topic_type == 'sensor_msgs/Image']
                    if image_topic in [topic[0] for topic in available_topics]:
                        rospy.loginfo("图像话题 %s 仍然存在", image_topic)
                    else:
                        rospy.logwarn("图像话题 %s 不存在，可用的图像话题: %s", image_topic, image_topics)

            if not image_received:
                rospy.logerr("等待图像超时，请检查摄像头是否正常工作")

                # 尝试使用OpenCV直接打开摄像头
                rospy.logwarn("尝试使用OpenCV直接打开摄像头...")
                try:
                    cap = cv2.VideoCapture(0)  # 尝试打开默认摄像头
                    if cap.isOpened():
                        ret, frame = cap.read()
                        if ret:
                            self.latest_frame = frame
                            self.frame_received = True
                            rospy.loginfo("成功使用OpenCV打开摄像头并获取图像，尺寸: %dx%d", frame.shape[1], frame.shape[0])
                            cap.release()
                        else:
                            rospy.logerr("无法从摄像头读取图像")
                            cap.release()
                            return False
                    else:
                        rospy.logerr("无法打开摄像头")
                        return False
                except Exception as e:
                    rospy.logerr("使用OpenCV打开摄像头时出错: %s", str(e))
                    return False

            rospy.loginfo("成功接收图像，开始旋转寻找香蕉...")

            # 记录起始角度和时间
            start_time = rospy.Time.now()
            current_angle = 0.0
            last_time = rospy.Time.now()

            # 旋转直到检测到香蕉或完成两圈
            print("\n" + "="*50)
            print("【开始旋转】: 正在旋转寻找香蕉...")
            print("【旋转速度】: %.2f rad/s (约 %.1f 度/秒)" % (cmd_vel.angular.z, cmd_vel.angular.z * 180 / math.pi))
            print("【旋转计划】: 将旋转720°(两圈)，每20°检测一次，共计36次检测")
            print("="*50 + "\n")

            # 创建一个全局窗口，避免重复创建
            cv2.namedWindow("YOLO检测", cv2.WINDOW_NORMAL)

            # 确保cmd_vel_pub已正确初始化
            if not hasattr(self, 'cmd_vel_pub') or self.cmd_vel_pub is None:
                self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
                rospy.loginfo("重新初始化cmd_vel发布者")
                rospy.sleep(0.5)  # 等待发布者初始化

            # 发送一个初始的停止命令，确保机器人从静止状态开始
            stop_cmd = Twist()
            self.cmd_vel_pub.publish(stop_cmd)
            rospy.sleep(0.5)

            # 旋转直到检测到香蕉或完成一圈
            while (rospy.Time.now() - start_time) < timeout and not banana_detected:
                # 发布速度命令
                self.cmd_vel_pub.publish(cmd_vel)

                # 每隔一段时间打印一次旋转状态，确保命令正在发送
                if int(current_angle * 180 / math.pi) % 10 == 0:  # 每旋转10度打印一次
                    rospy.loginfo("正在发送旋转命令: 角速度=%.2f rad/s", cmd_vel.angular.z)

                # 计算旋转角度
                current_time = rospy.Time.now()
                dt = (current_time - last_time).to_sec()
                current_angle += cmd_vel.angular.z * dt
                last_time = current_time

                # 记录上一次检测的角度，确保不会在同一个角度多次检测
                if not hasattr(self, 'last_detection_angle'):
                    self.last_detection_angle = -20  # 初始化为-20度，确保0度时会检测

                # 处理当前帧 - 每旋转20°进行一次检测
                current_angle_deg = int(current_angle * 180 / math.pi)
                if (self.latest_frame is not None and
                    current_angle_deg % 20 == 0 and
                    current_angle_deg > 0 and
                    current_angle_deg > self.last_detection_angle + 15):  # 确保与上次检测至少相差15度

                    # 更新上次检测角度
                    self.last_detection_angle = current_angle_deg

                    # 停止旋转，等待检测结果
                    stop_cmd = Twist()
                    self.cmd_vel_pub.publish(stop_cmd)
                    rospy.loginfo("已旋转 %d 度，停止等待检测结果...", current_angle_deg)
                    print(f"【旋转状态】: 已旋转 {current_angle_deg} 度，停止等待检测结果...")
                    # 每次检测都打印状态
                    print(f"【当前状态】: 已旋转 {current_angle_deg} 度，正在进行第 {current_angle_deg // 20} 次检测...")

                    # 使用YOLO进行目标检测
                    try:
                        # 记录当前图像时间戳
                        if hasattr(self, 'latest_frame_time'):
                            current_frame_time = self.latest_frame_time
                            rospy.loginfo("检测使用图像，时间戳: %.3f", current_frame_time)

                        # 复制当前帧，避免被回调函数修改
                        current_frame = self.latest_frame.copy()

                        # 检测前打印信息
                        if int(current_angle * 180 / math.pi) % 90 == 0:  # 每90度详细打印一次
                            rospy.loginfo("正在进行YOLO检测，当前角度: %.1f 度", current_angle * 180 / math.pi)

                        # 执行检测 - 使用多核CPU加速和动态获取的香蕉类别ID
                        results = model.predict(
                            source=current_frame,
                            conf=0.8,  # 提高置信度阈值到0.8
                            iou=0.45,
                            classes=[banana_class_id],  # 使用动态获取的香蕉类别ID
                            verbose=False,
                            device='cpu',
                            workers=num_workers  # 使用多核CPU
                        )

                        # 获取图像尺寸
                        height, width = current_frame.shape[:2]
                        center_x = width // 2

                        # 在图像上绘制检测结果
                        annotated_frame = results[0].plot()

                        # 将只读数组转换为可写数组
                        annotated_frame = np.array(annotated_frame, copy=True)

                        # 获取图像尺寸
                        height, width = annotated_frame.shape[:2]

                        # 绘制图像中心垂直线
                        cv2.line(annotated_frame, (center_x, 0), (center_x, height), (0, 0, 255), 1)

                        # 添加旋转角度信息
                        angle_text = f"旋转角度: {current_angle_deg}°"
                        cv2.putText(annotated_frame, angle_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                        # 添加检测次数信息
                        detection_count = current_angle_deg // 20
                        cv2.putText(annotated_frame, f"检测次数: {detection_count}/36", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                        # 显示检测窗口 - 确保窗口存在并更新
                        try:
                            cv2.imshow("YOLO检测", annotated_frame)
                            cv2.waitKey(1)  # 更新显示，但不等待按键
                        except Exception as e:
                            rospy.logwarn("显示图像时出错: %s", str(e))

                        # 检查是否有检测结果
                        if results and len(results) > 0 and len(results[0].boxes) > 0:
                            num_boxes = len(results[0].boxes)
                            # 检查是否有目标类别的物体 - 使用动态获取的类别ID列表
                            has_target = False
                            detected_class_id = None
                            detected_class_name = ""

                            for box in results[0].boxes:
                                cls = int(box.cls[0].item())
                                if cls in model.classes:  # 检查是否是我们要检测的类别
                                    has_target = True
                                    detected_class_id = cls
                                    detected_class_name = class_names[cls] if cls in class_names else f"未知类别{cls}"
                                    break

                            if has_target:
                                print(f"【检测结果】: 发现 {num_boxes} 个目标，其中包含{detected_class_name}(ID={detected_class_id})")
                                # 设置检测到的目标ID，用于后续处理
                                banana_class_id = detected_class_id
                            else:
                                # 获取要检测的类别名称列表
                                target_classes_names = [class_names[cls] if cls in class_names else f"未知类别{cls}" for cls in model.classes]
                                print(f"【检测结果】: 发现 {num_boxes} 个目标，但没有{'/'.join(target_classes_names)}")
                                # 恢复旋转
                                self.cmd_vel_pub.publish(cmd_vel)
                                rospy.loginfo("检测完成，继续旋转...")
                                print("【旋转状态】: 检测完成，继续旋转...")
                                # 短暂等待，确保命令被执行
                                rospy.sleep(0.5)
                        else:
                            # 在旋转阶段，如果没有检测到目标，继续旋转
                            print("【检测结果】: 未发现任何目标，继续旋转...")
                            # 恢复旋转
                            self.cmd_vel_pub.publish(cmd_vel)
                            rospy.loginfo("检测完成，继续旋转...")
                            print("【旋转状态】: 检测完成，继续旋转...")
                            # 短暂等待，确保命令被执行
                            rospy.sleep(0.5)
                            continue
                    except Exception as e:
                        rospy.logerr("YOLO检测出错: %s", str(e))
                        print(f"【检测错误】: {str(e)}")
                        # 恢复旋转
                        self.cmd_vel_pub.publish(cmd_vel)
                        rospy.loginfo("检测出错，继续旋转...")
                        print("【旋转状态】: 检测出错，继续旋转...")
                        # 短暂等待，确保命令被执行
                        rospy.sleep(0.5)
                        # 继续循环，不要因为一次检测失败就退出

                    # 检查是否检测到香蕉
                    for box in results[0].boxes:
                        cls = int(box.cls[0].item())
                        conf = box.conf[0].item()

                        # 如果是目标类别，并且置信度高于阈值 - 使用动态获取的类别ID
                        if cls in model.classes and conf > 0.8:  # 提高置信度阈值到0.8
                            # 获取边界框坐标
                            x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())

                            # 计算目标面积
                            area = (x2 - x1) * (y2 - y1)

                            # 计算目标中心点
                            center_x_obj = int((x1 + x2) / 2)
                            center_y_obj = int((y1 + y2) / 2)

                            # 获取图像尺寸
                            height, width = current_frame.shape[:2]
                            image_center_x = width // 2

                            # 计算偏移量
                            offset_x = center_x_obj - image_center_x
                            offset_pct = (offset_x / width) * 100

                            # 获取目标类别名称
                            target_name = class_names[cls] if cls in class_names else f"未知类别{cls}"

                            # 在ROS日志和控制台中明确通知检测到目标
                            detection_msg = f"检测到{target_name}! 置信度: {conf:.2f}, 面积: {area} 像素"
                            rospy.loginfo(detection_msg)
                            print("\n" + "="*50)
                            print(f"【检测结果】: 发现{target_name}!")
                            print(f"【类别ID】: {cls}")
                            print(f"【置信度】: {conf:.2f}")
                            print(f"【位置】: X={center_x_obj}, Y={center_y_obj}")
                            print(f"【偏移】: {offset_x:+d} 像素 ({offset_pct:+.1f}%)")
                            print(f"【大小】: {area} 像素")
                            print("="*50 + "\n")

                            # 在检测到目标时，在图像上标注更多信息
                            # 确保annotated_frame是可写的
                            if not hasattr(annotated_frame, 'flags') or not annotated_frame.flags.writeable:
                                annotated_frame = np.array(annotated_frame, copy=True)

                            # 添加目标信息
                            cv2.putText(annotated_frame, f"{target_name} ({conf:.2f})", (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                            cv2.putText(annotated_frame, f"ID: {cls}", (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                            cv2.putText(annotated_frame, f"面积: {area} 像素", (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                            cv2.putText(annotated_frame, f"偏移: {offset_x:+d} 像素", (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                            # 显示检测窗口 - 确保窗口存在并更新
                            try:
                                cv2.imshow("YOLO检测", annotated_frame)
                                cv2.waitKey(1)  # 更新显示，但不等待按键
                            except Exception as e:
                                rospy.logwarn("显示图像时出错: %s", str(e))

                            banana_detected = True
                            break

                # 检查是否检测到目标
                if banana_detected:
                    # 获取目标类别名称
                    target_name = ""
                    if detected_class_id is not None and detected_class_id in class_names:
                        target_name = class_names[detected_class_id]
                    else:
                        # 尝试从模型类别中获取第一个类别名称
                        for cls_id in model.classes:
                            if cls_id in class_names:
                                target_name = class_names[cls_id]
                                break
                        if not target_name:
                            target_name = "目标物体"

                    rospy.loginfo(f"检测到{target_name}！停止旋转")
                    print(f"【动作】: 检测到{target_name}，停止旋转")
                    break

                # 检查是否完成两圈旋转
                if current_angle >= 6 * math.pi:
                    # 获取要检测的类别名称列表
                    target_classes_names = []
                    for cls_id in model.classes:
                        if cls_id in class_names:
                            target_classes_names.append(class_names[cls_id])

                    target_str = "目标物体"
                    if target_classes_names:
                        target_str = "/".join(target_classes_names)

                    rospy.loginfo(f"完成720°旋转，未检测到{target_str}")
                    print("\n" + "="*50)
                    print("【旋转完成】: 已完成720°旋转")
                    print(f"【检测结果】: 未发现{target_str}")
                    print("="*50 + "\n")
                    break
                # 每完成一圈打印一次提示
                elif current_angle >= 2 * math.pi and not hasattr(self, 'first_circle_completed'):
                    self.first_circle_completed = True
                    print("\n" + "="*50)
                    print("【旋转进度】: 已完成第一圈(360°)，继续旋转第二圈...")
                    print("="*50 + "\n")

                # 显示当前旋转角度
                angle_deg = current_angle * 180 / math.pi
                if int(angle_deg) % 60 == 0 and int(angle_deg) > 0:  # 每旋转60度显示一次
                    rospy.loginfo("已旋转: %.1f 度", angle_deg)
                    # 计算完成百分比
                    percent_complete = min(100, (angle_deg / 720.0) * 100)
                    # 显示当前圈数
                    current_circle = 1 if angle_deg <= 360 else 2
                    circle_angle = angle_deg if angle_deg <= 360 else angle_deg - 360
                    print(f"【旋转进度】: 第{current_circle}圈 {circle_angle:.1f}° / 总计 {angle_deg:.1f}° ({percent_complete:.1f}%)")

                rate.sleep()

            # 停止小车
            cmd_vel.angular.z = 0.0
            self.cmd_vel_pub.publish(cmd_vel)

            # 关闭所有OpenCV窗口
            cv2.destroyAllWindows()
            rospy.loginfo("已关闭所有图像显示窗口")

            # 取消订阅图像话题
            image_sub.unregister()

            # 显示结果
            if banana_detected:
                # 获取目标类别名称
                target_name = ""
                if detected_class_id is not None and detected_class_id in class_names:
                    target_name = class_names[detected_class_id]
                else:
                    # 尝试从模型类别中获取第一个类别名称
                    for cls_id in model.classes:
                        if cls_id in class_names:
                            target_name = class_names[cls_id]
                            break
                    if not target_name:
                        target_name = "目标物体"

                result_msg = f"成功检测到{target_name}，旋转了 {current_angle * 180 / math.pi:.1f} 度"
                rospy.loginfo(result_msg)
                print("\n" + "="*50)
                print(f"【任务完成】: 成功检测到{target_name}!")
                print(f"【目标类别】: ID={detected_class_id if detected_class_id is not None else '未知'}")
                print(f"【旋转角度】: {current_angle * 180 / math.pi:.1f} 度")
                print("="*50 + "\n")

                # 检测到目标后，使用PID控制靠近目标
                # 获取目标类别名称
                target_name = ""
                if detected_class_id is not None and detected_class_id in class_names:
                    target_name = class_names[detected_class_id]
                else:
                    # 尝试从模型类别中获取第一个类别名称
                    for cls_id in model.classes:
                        if cls_id in class_names:
                            target_name = class_names[cls_id]
                            break
                    if not target_name:
                        target_name = "目标物体"

                rospy.loginfo(f"开始使用PID控制靠近{target_name}...")

                # 直接创建窗口，如果已存在则不会有影响
                try:
                    # 尝试安全地创建窗口
                    cv2.namedWindow("YOLO检测", cv2.WINDOW_NORMAL)
                except Exception as e:
                    rospy.logwarn("创建窗口时出错: %s", str(e))

                # 创建PID控制器
                # 图像中心X坐标PID控制器
                image_height, image_width = self.latest_frame.shape[:2]
                center_x = image_width // 2
                # 减小积分项，避免积分饱和
                pid_x = PIDController(kp=0.1, ki=0.0000, kd=0.000, setpoint=center_x)
                # 设置输出限制，避免过大的输出
                pid_x.output_limits = (-0.5, 0.5)

                # 目标面积PID控制器 - 增大目标面积以更快接近目标
                target_area = 40000  # 目标面积（像素）- 从30000增加到40000
                # 增大比例系数，使响应更快
                pid_area = PIDController(kp=0.0001, ki=0.000000, kd=0.00000, setpoint=target_area)
                # 增大输出限制，使小车能够更快移动
                pid_area.output_limits = (-0.5, 0.5)

                # PID控制参数
                pid_interval = 0.1  # PID计算间隔（秒）
                last_pid_time = time.time()
                last_x_output = 0
                last_area_output = 0
                last_target_x = center_x
                last_target_area = 0

                # 控制模式
                control_enabled = True
                forward_mode = True  # 启用前进模式

                # 创建一个新的速度消息
                pid_cmd_vel = Twist()

                # PID控制超时
                pid_timeout = 60.0  # 60秒超时
                pid_start_time = time.time()

                # PID控制循环
                rate = rospy.Rate(10)  # 降低到10Hz，确保有足够时间处理每一帧

                rospy.loginfo("PID控制开始，目标面积: %d 像素", target_area)

                # 添加计数器，用于记录图像处理次数
                frame_count = 0
                detection_count = 0

                # 直接使用ROS图像话题进行PID控制
                rospy.loginfo("使用ROS图像话题进行PID控制...")

                # 强制重新订阅图像话题，确保获取实时图像
                rospy.logwarn("为PID控制重新订阅图像话题...")

                # 如果存在旧的订阅者，先取消订阅
                if hasattr(self, 'pid_image_sub') and self.pid_image_sub is not None:
                    self.pid_image_sub.unregister()

                # 定义新的图像回调函数
                def pid_image_callback(msg):
                    try:
                        # 将ROS图像转换为OpenCV格式
                        self.latest_frame = self.bridge.imgmsg_to_cv2(msg, "bgr8")
                        self.frame_received = True
                        # 添加时间戳，用于检查图像是否更新
                        self.latest_frame_time = time.time()
                        # 打印调试信息（每5帧打印一次）
                        if not hasattr(self, 'pid_frame_count'):
                            self.pid_frame_count = 0
                        self.pid_frame_count += 1
                        if self.pid_frame_count % 5 == 0:
                            rospy.loginfo("PID控制：收到新图像，时间戳: %.3f", self.latest_frame_time)
                    except Exception as e:
                        rospy.logerr("PID控制：处理图像时出错: %s", str(e))

                # 初始化时间戳
                self.latest_frame_time = time.time()

                # 检查可用的图像话题
                available_topics = rospy.get_published_topics()
                image_topics = [topic for topic, topic_type in available_topics if topic_type == 'sensor_msgs/Image']

                # 优先使用校正后的图像话题
                if '/usb_cam/image_calibrated' in image_topics:
                    image_topic = '/usb_cam/image_calibrated'
                    rospy.loginfo("使用校正后的图像话题: %s", image_topic)
                elif '/usb_cam/image_raw' in image_topics:
                    image_topic = '/usb_cam/image_raw'
                    rospy.loginfo("使用原始图像话题: %s", image_topic)
                else:
                    # 如果没有找到预期的话题，使用第一个可用的图像话题
                    if image_topics:
                        image_topic = image_topics[0]
                        rospy.loginfo("使用可用的图像话题: %s", image_topic)
                    else:
                        rospy.logerr("未找到任何图像话题，无法进行检测")
                        return False

                # 重新订阅图像话题，使用新的回调函数
                self.pid_image_sub = rospy.Subscriber(image_topic, Image, pid_image_callback)
                rospy.loginfo("已订阅图像话题: %s 用于PID控制", image_topic)

                # 等待接收图像
                wait_start = time.time()
                self.frame_received = False  # 重置标志
                while not self.frame_received and time.time() - wait_start < 5.0:  # 5秒超时
                    rospy.sleep(0.1)
                    rospy.loginfo("等待接收PID控制的第一帧图像...")

                if not self.frame_received:
                    rospy.logerr("等待图像超时，PID控制失败")
                    return False

                rospy.loginfo("成功接收到PID控制的第一帧图像，时间戳: %.3f", self.latest_frame_time)

                try:
                    # PID控制循环 - 这个循环会被下面的主循环替代
                    # 只是为了初始化一些变量和进行一些基本检查
                    if self.latest_frame is None:
                        rospy.logwarn("当前帧为空，等待新图像...")
                        rospy.sleep(1.0)
                        if self.latest_frame is None:
                            rospy.logerr("无法获取图像，PID控制失败")
                            return False

                    # 复制当前帧，避免被回调函数修改
                    current_frame = self.latest_frame.copy()

                    # 图像处理计数初始化
                    frame_count = 0

                    # 打印图像信息
                    height, width = current_frame.shape[:2]
                    rospy.loginfo("获取到图像，尺寸: %dx%d，准备开始检测", width, height)
                    print(f"\n{'='*50}")
                    print(f"【准备开始】: 图像尺寸 {width}x{height}")
                    print(f"【检测目标】: 香蕉")
                    print(f"{'='*50}\n")

                except Exception as e:
                    rospy.logerr("使用ROS图像话题时出错: %s", str(e))
                    rospy.logerr(traceback.format_exc())
                    return False

                # 记录上一次处理的图像时间戳和帧
                last_processed_time = 0
                last_frame_count = 0

                # 添加调试变量，用于检测是否获取到新图像
                no_new_image_count = 0

                # 继续PID控制循环
                while time.time() - pid_start_time < pid_timeout and control_enabled:
                    # 等待新的图像帧
                    if self.latest_frame is None:
                        rospy.logwarn("当前帧为空，等待新图像...")
                        rospy.sleep(0.1)
                        continue

                    # 检查是否有新图像
                    if hasattr(self, 'latest_frame_time') and self.latest_frame_time <= last_processed_time:
                        # 如果没有新图像，短暂等待并增加计数
                        no_new_image_count += 1
                        if no_new_image_count % 50 == 0:  # 每50次检查打印一次
                            rospy.logwarn("等待新图像中...已等待 %.1f 秒", no_new_image_count * 0.01)
                            rospy.logwarn("上次图像时间戳: %.3f, 当前时间: %.3f", last_processed_time, time.time())
                        rospy.sleep(0.01)
                        continue

                    # 重置计数器
                    no_new_image_count = 0

                    # 记录当前图像时间戳
                    if hasattr(self, 'latest_frame_time'):
                        last_processed_time = self.latest_frame_time
                        # 每5帧打印一次时间戳信息
                        if frame_count % 5 == 0 or frame_count - last_frame_count >= 5:
                            rospy.loginfo("处理新图像，时间戳: %.3f, 帧间隔: %d", last_processed_time, frame_count - last_frame_count)
                            last_frame_count = frame_count

                    # 复制当前帧，避免被回调函数修改
                    current_frame = self.latest_frame.copy()

                    # 打印图像尺寸，确认图像有效
                    if frame_count % 10 == 0:
                        height, width = current_frame.shape[:2]
                        rospy.loginfo("当前图像尺寸: %dx%d", width, height)

                    # 增加帧计数
                    frame_count += 1

                    # 使用YOLO进行目标检测
                    try:
                        # 减少日志输出，只在每5帧打印一次
                        if frame_count % 5 == 0:
                            print(f"【检测】: 第 {frame_count} 帧")

                        # 获取图像尺寸但不打印
                        height, width = current_frame.shape[:2]

                        # 使用多核CPU加速YOLO检测 - 使用动态获取的香蕉类别ID
                        results = model.predict(
                            source=current_frame,
                            conf=0.25,
                            iou=0.45,
                            classes=[banana_class_id],  # 使用动态获取的香蕉类别ID
                            verbose=False,
                            device='cpu',
                            workers=num_workers  # 使用多核CPU
                        )

                        # 检查检测结果
                        if results and len(results) > 0:
                            num_boxes = len(results[0].boxes)
                            # 检查是否有香蕉类别的目标 - 使用动态获取的香蕉类别ID
                            has_banana = False
                            for box in results[0].boxes:
                                cls = int(box.cls[0].item())
                                if cls == banana_class_id:  # 使用动态获取的香蕉类别ID
                                    has_banana = True
                                    break

                            if has_banana:
                                print(f"【检测结果】: 发现 {num_boxes} 个目标，其中包含香蕉")
                            else:
                                # 明确告知在目标追踪过程中丢失了香蕉并停止
                                print("\n" + "="*50)
                                print("【检测结果】: 目标追踪过程中丢失了香蕉！")
                                print("【动作】: 停止追踪")
                                print("="*50 + "\n")
                                # 停止小车
                                pid_cmd_vel.linear.x = 0.0
                                pid_cmd_vel.angular.z = 0.0
                                self.cmd_vel_pub.publish(pid_cmd_vel)
                                # 取消订阅图像话题
                                image_sub.unregister()
                                return False
                        else:
                            # 明确告知在目标追踪过程中丢失了目标并停止
                            print("\n" + "="*50)
                            print("【检测结果】: 目标追踪过程中丢失了所有目标！")
                            print("【动作】: 停止追踪")
                            print("="*50 + "\n")
                            # 停止小车
                            pid_cmd_vel.linear.x = 0.0
                            pid_cmd_vel.angular.z = 0.0
                            self.cmd_vel_pub.publish(pid_cmd_vel)
                            # 取消订阅图像话题
                            image_sub.unregister()
                            return False

                        # 查找要跟踪的目标
                        target_x = None
                        target_confidence = 0
                        target_box = None
                        target_area = 0

                        for box in results[0].boxes:
                            cls = int(box.cls[0].item())
                            conf = box.conf[0].item()

                            # 不再打印每个检测到的目标，减少日志输出

                            # 如果是香蕉类别，并且置信度高于当前目标 - 使用动态获取的香蕉类别ID
                            if cls == banana_class_id and conf > target_confidence:
                                # 获取边界框坐标
                                x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())

                                # 计算目标中心点X坐标
                                obj_x = (x1 + x2) // 2

                                # 计算目标面积
                                area = (x2 - x1) * (y2 - y1)

                                # 更新目标信息
                                target_x = obj_x
                                target_confidence = conf
                                target_box = (x1, y1, x2, y2)
                                target_area = area

                                # 打印香蕉目标信息
                                rospy.loginfo("找到香蕉: 位置=(%d,%d,%d,%d), 中心点=%d, 面积=%d, 置信度=%.2f",
                                            x1, y1, x2, y2, obj_x, area, conf)

                                # 计算重心位置
                                center_x_img = width // 2
                                center_y_img = height // 2
                                obj_y = (y1 + y2) // 2

                                # 计算重心偏移（像素和百分比）
                                x_offset = obj_x - center_x_img
                                y_offset = obj_y - center_y_img
                                x_offset_pct = (x_offset / width) * 100
                                y_offset_pct = (y_offset / height) * 100

                                # 打印重心偏移和面积信息
                                print(f"\n{'='*50}")
                                print(f"【第 {frame_count} 帧检测结果】")
                                print(f"【目标重心】: X={obj_x}, Y={obj_y}")
                                print(f"【图像中心】: X={center_x_img}, Y={center_y_img}")
                                print(f"【重心偏移】: X={x_offset:+d}像素({x_offset_pct:+.1f}%), Y={y_offset:+d}像素({y_offset_pct:+.1f}%)")
                                print(f"【目标面积】: {area}像素 ({(area/(width*height)*100):.2f}%的图像面积)")
                                print(f"【目标尺寸】: 宽={x2-x1}像素, 高={y2-y1}像素")
                                print(f"【置信度】: {conf:.2f}")
                                print(f"{'='*50}\n")

                                detection_count += 1
                    except Exception as e:
                        rospy.logerr("YOLO检测出错: %s", str(e))
                        rospy.logerr(traceback.format_exc())

                    # 如果检测到目标，更新目标位置和面积
                    if target_x is not None:
                        last_target_x = target_x
                        last_target_area = target_area

                        # 每次检测到目标时都计算PID，确保实时响应
                        # 计算X方向PID
                        x_output, x_terms = pid_x.compute(target_x)

                        # 计算面积PID
                        area_output, area_terms = pid_area.compute(target_area)

                        last_pid_time = time.time()
                        last_x_output = x_output
                        last_area_output = area_output
                        pid_just_updated = True
                    else:
                        # 如果当前帧没有检测到目标，使用上次的PID输出
                        x_output = last_x_output
                        area_output = last_area_output
                        pid_just_updated = False

                    # 如果找到了目标，计算PID控制输出并发送控制命令
                    if target_x is not None and target_box is not None:
                        x1, y1, x2, y2 = target_box

                        # 显示距离中心的偏差
                        error_pixels = center_x - target_x

                        # 在控制台打印信息
                        pid_status = "已更新" if pid_just_updated else "使用缓存"
                        rospy.loginfo("检测到香蕉! 偏差: %+d 像素, 面积: %d 像素, 目标面积: %d 像素, X-PID: %.2f, 面积-PID: %.4f (%s)",
                                    error_pixels, target_area, pid_area.setpoint, x_output, area_output, pid_status)

                        # 计算目标重心的Y坐标
                        x1, y1, x2, y2 = target_box
                        obj_y = (y1 + y2) // 2
                        center_y_img = height // 2

                        # 计算重心偏移（像素和百分比）
                        x_offset = target_x - center_x
                        y_offset = obj_y - center_y_img
                        x_offset_pct = (x_offset / width) * 100
                        y_offset_pct = (y_offset / height) * 100

                        # 计算面积比例
                        area_ratio = (target_area / (width * height)) * 100
                        area_diff = target_area - pid_area.setpoint
                        area_diff_pct = (area_diff / pid_area.setpoint) * 100

                        # 打印更详细的目标位置信息
                        print(f"\n{'='*50}")
                        print(f"【PID控制】: 第 {frame_count} 帧")
                        print(f"【重心位置】: X={target_x}, Y={obj_y}")
                        print(f"【重心偏移】: X={x_offset:+d}像素({x_offset_pct:+.1f}%), Y={y_offset:+d}像素({y_offset_pct:+.1f}%)")
                        print(f"【目标面积】: {target_area}像素 ({area_ratio:.2f}%的图像面积)")
                        print(f"【面积差异】: {area_diff:+d}像素({area_diff_pct:+.1f}%) 相对目标值{pid_area.setpoint}像素")
                        print(f"【PID输出】: X方向={x_output:.2f}, 面积方向={area_output:.4f}")
                        print(f"{'='*50}\n")

                        # 根据前进模式和面积PID输出设置线速度
                        if forward_mode:
                            # 使用面积PID输出控制前进/后退
                            linear_x = area_output

                            # 限制线速度范围
                            max_linear = 0.15  # 降低最大线速度以提高稳定性
                            min_linear = -0.1  # 降低最小线速度以提高稳定性

                            # 如果线速度太小，设为零
                            if abs(linear_x) < 0.02:  # 降低死区阈值
                                linear_x = 0.0
                            else:
                                # 应用平滑函数，使速度变化更加平滑
                                linear_x = max(min_linear, min(max_linear, linear_x))

                                # 根据目标面积与设定值的接近程度调整速度
                                area_ratio = min(1.0, abs(target_area - pid_area.setpoint) / pid_area.setpoint)
                                # 当接近目标时，降低速度
                                # linear_x *= area_ratio
                        else:
                            linear_x = 0.0  # 前进模式关闭时不前进

                        # 使用更平滑的方式将PID输出转换为角速度
                        # 使用非线性映射，使小偏差时转向更精确，大偏差时转向更快
                        error_ratio = min(1.0, abs(error_pixels) / (center_x * 0.5))  # 归一化偏差
                        max_angular = 0.3  # 降低最大角速度以提高稳定性

                        if abs(error_pixels) < 20:  # 小偏差时，精确控制
                            angular_z = -error_pixels * 0.003  # 降低系数以减小响应
                        else:  # 大偏差时，快速转向
                            angular_z = -max_angular * error_ratio * (1 if error_pixels > 0 else -1)

                        # 限制角速度范围
                        angular_z = max(-max_angular, min(max_angular, angular_z))

                        # 在控制台打印电机控制值
                        rospy.loginfo("电机控制 - 线速度: %.2f m/s, 角速度: %.2f rad/s", linear_x, angular_z)

                        # 计算预期的运动效果
                        expected_x_movement = linear_x * 0.1  # 0.1秒内的预期前进距离(米)
                        expected_angle_movement = angular_z * 0.1 * 180 / math.pi  # 0.1秒内的预期角度变化(度)

                        # 根据控制命令预测下一帧的位置变化
                        if abs(angular_z) > 0.01:  # 如果有明显的角速度
                            # 估算角度变化对X偏移的影响（简化计算）
                            expected_x_change = -expected_angle_movement * 2  # 粗略估计：每转动1度，目标在图像中移动约2像素
                        else:
                            expected_x_change = 0

                        # 估算前进/后退对面积的影响（简化计算）
                        if abs(linear_x) > 0.01:  # 如果有明显的线速度
                            # 粗略估计：每前进0.01米，目标面积增加约1%
                            expected_area_change = (expected_x_movement / 0.01) * target_area * 0.01
                        else:
                            expected_area_change = 0

                        print(f"【控制命令】: 线速度={linear_x:.2f} m/s, 角速度={angular_z:.2f} rad/s")
                        print(f"【预期效果】: X偏移变化约{expected_x_change:.1f}像素, 面积变化约{expected_area_change:.0f}像素")
                        print(f"【控制目的】: {'向左转' if angular_z > 0 else '向右转' if angular_z < 0 else '保持方向'}, {'前进' if linear_x > 0 else '后退' if linear_x < 0 else '保持距离'}")

                        # 直接发送速度命令
                        pid_cmd_vel.linear.x = linear_x
                        pid_cmd_vel.angular.z = angular_z
                        self.cmd_vel_pub.publish(pid_cmd_vel)

                        # 给机器人一些时间来执行命令，但减少等待时间以提高响应速度
                        rospy.sleep(0.1)  # 减少暂停时间，提高响应速度

                        # 记录当前状态，用于下一帧比较
                        if not hasattr(self, 'last_target_x'):
                            self.last_target_x = target_x
                            self.last_target_area = target_area
                        else:
                            # 计算实际变化
                            actual_x_change = target_x - self.last_target_x
                            actual_area_change = target_area - self.last_target_area

                            # 更新记录
                            self.last_target_x = target_x
                            self.last_target_area = target_area

                            # 如果不是第一帧，打印实际变化
                            if frame_count > 1:
                                print(f"【实际变化】: X偏移变化了{actual_x_change:+.1f}像素, 面积变化了{actual_area_change:+.0f}像素")

                        # 在图像上绘制检测结果
                        annotated_frame = results[0].plot()

                        # 将只读数组转换为可写数组
                        annotated_frame = np.array(annotated_frame, copy=True)

                        # 获取图像尺寸
                        height, width = annotated_frame.shape[:2]

                        # 绘制图像中心垂直线
                        cv2.line(annotated_frame, (center_x_img, 0), (center_x_img, height), (0, 0, 255), 1)

                        # 添加PID控制信息
                        cv2.putText(annotated_frame, f"帧: {frame_count}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

                        # 显示检测窗口 - 确保窗口存在并更新
                        try:
                            cv2.imshow("YOLO检测", annotated_frame)
                            cv2.waitKey(1)  # 更新显示，但不等待按键
                        except Exception as e:
                            rospy.logwarn("显示图像时出错: %s", str(e))

                        # 检查是否达到目标面积和位置
                        # 使用更复杂的条件，确保稳定达到目标
                        area_error = abs(target_area - pid_area.setpoint)  # 计算当前面积与目标面积的差异
                        position_error = abs(error_pixels)  # 计算位置偏差

                        # 打印当前误差值，便于调试
                        print(f"【当前误差】: 位置偏差={position_error}像素, 面积偏差={area_error}像素")

                        # 计算稳定时间
                        if not hasattr(self, 'stable_start_time'):
                            self.stable_start_time = None
                            self.stable_count = 0

                        # 检查是否在目标范围内 - 进一步放宽条件
                        if area_error < 8000 and position_error < 80:  # 更宽松的条件，允许更大的误差
                            # 增加稳定计数
                            self.stable_count += 1

                            # 打印稳定进度 - 更新为新的阈值
                            if self.stable_count % 1 == 0:  # 每次都打印
                                print(f"【稳定进度】: {self.stable_count}/3 (位置偏差: {position_error} 像素, 面积偏差: {area_error} 像素)")

                            # 如果稳定计数达到阈值，开始计时 - 进一步降低阈值
                            if self.stable_count >= 3 and self.stable_start_time is None:  # 从5降低到3
                                self.stable_start_time = time.time()
                                rospy.loginfo("目标位置稳定，开始计时...")
                                print(f"\n{'='*50}")
                                print("【状态】: 目标位置稳定，开始计时...")
                                print(f"{'='*50}\n")

                            # 如果稳定时间达到阈值，完成PID控制 - 进一步降低时间
                            if self.stable_start_time is not None and time.time() - self.stable_start_time >= 0.5:  # 从1.0降低到0.5
                                rospy.loginfo("已稳定达到目标面积和位置，PID控制完成")
                                print(f"\n{'='*50}")
                                print("【任务完成】: 已稳定追踪到目标")
                                print(f"【最终位置】: 偏差 {position_error} 像素")
                                print(f"【最终大小】: 面积 {target_area} 像素 (目标: {pid_area.setpoint})")
                                print(f"{'='*50}\n")
                                break
                        else:
                            # 重置稳定计数和时间
                            if self.stable_count > 0:
                                self.stable_count = 0
                                self.stable_start_time = None
                                print("【状态】: 目标不稳定，重置计数器")
                    else:
                        # 如果没有找到目标，显示提示信息
                        rospy.loginfo("未检测到香蕉!")

                        # 停止小车
                        pid_cmd_vel.linear.x = 0.0
                        pid_cmd_vel.angular.z = 0.0
                        self.cmd_vel_pub.publish(pid_cmd_vel)

                    # 检查是否超时
                    if time.time() - pid_start_time >= pid_timeout:
                        rospy.loginfo("PID控制超时")
                        break

                    # 控制循环速率
                    rate.sleep()

                # 停止小车
                pid_cmd_vel.linear.x = 0.0
                pid_cmd_vel.angular.z = 0.0
                self.cmd_vel_pub.publish(pid_cmd_vel)

                # 取消订阅PID控制的图像话题
                if hasattr(self, 'pid_image_sub') and self.pid_image_sub is not None:
                    self.pid_image_sub.unregister()
                    rospy.loginfo("已取消订阅PID控制的图像话题")

                # 关闭所有OpenCV窗口
                cv2.destroyAllWindows()
                rospy.loginfo("已关闭所有图像显示窗口")

                # 打印统计信息
                rospy.loginfo("PID控制结束，统计信息:")
                rospy.loginfo("- 处理图像帧数: %d", frame_count)
                rospy.loginfo("- 检测到香蕉次数: %d", detection_count)

                # 如果没有检测到香蕉，打印可能的原因
                if detection_count == 0:
                    rospy.logwarn("整个PID控制过程中未检测到香蕉，可能的原因:")
                    rospy.logwarn("1. 图像获取失败或图像质量问题")
                    rospy.logwarn("2. YOLO模型未正确加载或配置")
                    rospy.logwarn("3. 视野中没有香蕉或香蕉不在检测范围内")
                    rospy.logwarn("4. 香蕉类别ID不正确（当前使用ID=%d）", banana_class_id)

                    # 检查模型支持的类别
                    if hasattr(model, 'names'):
                        rospy.loginfo("模型支持的类别: %s", model.names)
                        if banana_class_id in model.names:
                            rospy.loginfo("类别ID %d 对应的名称: %s", banana_class_id, model.names[banana_class_id])
                        else:
                            rospy.logwarn("模型不支持类别ID %d", banana_class_id)

                rospy.loginfo("PID控制结束")

            elif (rospy.Time.now() - start_time) >= timeout:
                rospy.loginfo("检测超时，未检测到香蕉")
                print("\n" + "="*50)
                print("【任务结果】: 检测超时，未检测到香蕉")
                print("【原因】: 超过了最大等待时间")
                print("="*50 + "\n")
            else:
                rospy.loginfo("完成360°旋转，未检测到香蕉")
                print("\n" + "="*50)
                print("【任务结果】: 未检测到香蕉")
                print("【原因】: 已完成360°旋转但未发现目标")
                print("="*50 + "\n")

            return banana_detected

        except Exception as e:
            rospy.logerr("香蕉检测时出错: %s" % str(e))
            rospy.logerr(traceback.format_exc())
            # 确保停止小车
            cmd_vel.angular.z = 0.0
            self.cmd_vel_pub.publish(cmd_vel)
            # 关闭所有OpenCV窗口
            try:
                cv2.destroyAllWindows()
                rospy.loginfo("已关闭所有图像显示窗口")
            except:
                pass
            return False

    def detection_callback(self, msg):
        """YOLO检测结果回调函数"""
        try:
            # 解析检测结果
            detection_data = msg.data
            if "banana" in detection_data.lower():
                rospy.loginfo("YOLO检测到香蕉: %s", detection_data)
                self.banana_detected = True
        except Exception as e:
            rospy.logerr("处理检测结果时出错: %s" % str(e))

    def detect_traffic_light(self):
        """红绿灯检测功能，返回检测到的红绿灯状态"""
        rospy.loginfo("开始检测红绿灯...")
        print("\n" + "="*50)
        print("【任务】: 红绿灯检测")
        print("="*50 + "\n")

        # 获取当前导航点索引，用于确定是哪个红绿灯
        current_waypoint_index = self.current_waypoint
        # 根据导航点索引确定红绿灯编号
        if current_waypoint_index == 2:  # 索引2是红绿灯1
            traffic_light_number = 1
        elif current_waypoint_index == 3:  # 索引3是红绿灯2
            traffic_light_number = 2
        else:
            traffic_light_number = current_waypoint_index + 1  # 默认逻辑

        rospy.loginfo(f"当前检测的是红绿灯{traffic_light_number} (导航点索引: {current_waypoint_index})")
        print(f"【信息】: 当前检测的是红绿灯{traffic_light_number} (导航点索引: {current_waypoint_index})")

        try:
            # 加载红绿灯检测模型
            model_path = "/home/<USER>/ucar_ws/my_code/red.pt"
            if not os.path.exists(model_path):
                rospy.logerr("红绿灯检测模型不存在: %s", model_path)
                print(f"【错误】: 红绿灯检测模型不存在: {model_path}")
                return False

            rospy.loginfo("加载红绿灯检测模型: %s", model_path)
            print(f"【状态】: 加载红绿灯检测模型: {model_path}")
            model = YOLO(model_path)

            # 获取模型支持的类别
            class_names = model.names
            rospy.loginfo("模型支持的类别: %s", class_names)
            print(f"【信息】: 模型支持的类别: {class_names}")

            # 创建保存目录
            save_dir = "/home/<USER>/ucar_ws/my_code/pic"
            os.makedirs(save_dir, exist_ok=True)

            # 订阅相机图像话题
            self.bridge = CvBridge()
            self.latest_frame = None
            self.frame_received = False

            def image_callback(msg):
                try:
                    self.latest_frame = self.bridge.imgmsg_to_cv2(msg, "bgr8")
                    self.frame_received = True
                except Exception as e:
                    rospy.logerr("图像转换错误: %s", e)

            # 订阅图像话题
            image_sub = rospy.Subscriber('/usb_cam/image_raw', Image, image_callback)

            # 等待接收图像
            rospy.loginfo("等待接收图像...")
            print("【状态】: 等待接收图像...")

            timeout = 5  # 5秒超时
            start_time = time.time()

            while not self.frame_received and time.time() - start_time < timeout:
                rospy.sleep(0.1)

            if not self.frame_received:
                rospy.logerr("未能接收到图像，检测失败")
                print("【错误】: 未能接收到图像，检测失败")
                image_sub.unregister()
                return False

            # 复制当前帧，避免在处理过程中被回调函数修改
            current_frame = self.latest_frame.copy()

            # 执行检测
            rospy.loginfo("执行红绿灯检测...")
            print("【状态】: 执行红绿灯检测...")

            results = model.predict(
                source=current_frame,
                conf=0.25,
                iou=0.45,
                verbose=False,
                device='cpu',
                workers=num_workers  # 使用多核CPU
            )

            # 保存原始图像
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            img_filename = os.path.join(save_dir, f"traffic_light_original_{timestamp}.jpg")
            cv2.imwrite(img_filename, current_frame)
            rospy.loginfo("已保存原始图像到: %s", img_filename)
            print(f"【信息】: 已保存原始图像到: {img_filename}")

            # 处理检测结果
            detection_results = []
            is_green = False  # 默认不是绿灯
            rospy.loginfo("初始化绿灯状态: %s", is_green)
            print(f"【初始状态】: 绿灯状态 = {is_green}")

            if len(results) > 0 and len(results[0].boxes) > 0:
                # 获取检测结果
                boxes = results[0].boxes

                for i, box in enumerate(boxes):
                    # 获取类别和置信度
                    cls_id = int(box.cls[0].item())
                    conf = box.conf[0].item()

                    # 获取类别名称
                    cls_name = class_names[cls_id] if cls_id in class_names else f"未知类别{cls_id}"

                    # 获取边界框坐标
                    x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())

                    # 计算中心点和面积
                    center_x = (x1 + x2) // 2
                    center_y = (y1 + y2) // 2
                    area = (x2 - x1) * (y2 - y1)

                    # 添加到检测结果列表
                    detection_results.append({
                        'class_id': cls_id,
                        'class_name': cls_name,
                        'confidence': conf,
                        'box': (x1, y1, x2, y2),
                        'center': (center_x, center_y),
                        'area': area
                    })

                    # 打印检测结果
                    rospy.loginfo("检测到 %s: 置信度=%.2f, 位置=(%d,%d), 面积=%d",
                                 cls_name, conf, center_x, center_y, area)
                    print(f"【检测结果】: 检测到 {cls_name}")
                    print(f"【置信度】: {conf:.2f}")
                    print(f"【位置】: ({center_x},{center_y})")
                    print(f"【面积】: {area} 像素")
                    print("-" * 30)

                    # 检查是否是绿灯
                    if "green" in cls_name.lower() or "绿灯" in cls_name:
                        is_green = True
                        rospy.loginfo("检测到绿灯! 类别名称: %s, 置信度: %.2f", cls_name, conf)
                        print("\n" + "="*50)
                        print(f"【重要】: 检测到绿灯!")
                        print(f"【类别名称】: {cls_name}")
                        print(f"【置信度】: {conf:.2f}")
                        print("="*50 + "\n")

            # 显示检测结果摘要
            if detection_results:
                print("\n" + "="*50)
                print(f"【检测完成】: 共检测到 {len(detection_results)} 个目标")

                # 按类别统计
                class_counts = {}
                for result in detection_results:
                    cls_name = result['class_name']
                    if cls_name in class_counts:
                        class_counts[cls_name] += 1
                    else:
                        class_counts[cls_name] = 1

                for cls_name, count in class_counts.items():
                    print(f"【{cls_name}】: {count} 个")

                print("="*50 + "\n")
            else:
                print("\n" + "="*50)
                print("【检测完成】: 未检测到任何目标")
                print("="*50 + "\n")

            # 取消订阅图像话题
            image_sub.unregister()

            # 调试信息：打印检测到的所有类别和绿灯状态
            print("\n" + "="*50)
            print("【调试】: 检测到的所有类别:")
            for result in detection_results:
                print(f"  - {result['class_name']} (置信度: {result['confidence']:.2f})")
            print(f"【绿灯状态】: {'是' if is_green else '否'}")
            print(f"【当前红绿灯】: 红绿灯{traffic_light_number}")
            print("="*50 + "\n")

            # 根据红绿灯状态和编号设置下一个导航点
            if traffic_light_number == 1:  # 红绿灯1
                if is_green:
                    # 如果红绿灯1是绿灯，播放路口1音频，然后导航到路口1，并设置完成标志
                    rospy.loginfo("红绿灯1是绿灯，播放路口1音频并导航到路口1")
                    print("【决策】: 红绿灯1是绿灯，播放路口1音频并导航到路口1")

                    # 播放路口1音频
                    self.play_audio_file("/home/<USER>/ucar_ws/my_code/voice/路口1.wav", "路口1")

                    # 设置下一个导航点为路口1
                    self.next_waypoint = 4  # 索引4对应路口1 ([-15.400, -0.100,1.098,"",0,30])
                    self.navigation_complete_after_next = True  # 设置完成标志
                else:
                    # 如果红绿灯1不是绿灯（红灯），继续检测红绿灯2
                    self.traffic_light_1_is_red = True  # 设置红绿灯1是红灯的标志
                    self.next_waypoint = 3  # 索引3对应红绿灯2 ([-16.884, 0.516,-2.2,"detect_traffic_light",0,30])
                    rospy.loginfo("红绿灯1是红灯，将继续到红绿灯2位置")
                    print("【决策】: 红绿灯1是红灯，将继续到红绿灯2位置")
                    print("【状态】: 设置红绿灯1是红灯标志，红绿灯2将跳过检测")
            elif traffic_light_number == 2:  # 红绿灯2
                # 到达红绿灯2，不需要检测红绿灯状态，直接播放路口2音频并导航到路口2
                rospy.loginfo("到达红绿灯2位置，播放路口2音频并导航到路口2")
                print("【决策】: 到达红绿灯2位置，播放路口2音频并导航到路口2")
                print("【说明】: 红绿灯1是红灯时的路径，直接前往路口2")

                # 播放路口2音频
                self.play_audio_file("/home/<USER>/ucar_ws/my_code/voice/路口2.wav", "路口2")

                # 设置下一个导航点为路口2
                self.next_waypoint = 5  # 索引5对应路口2 ([-16.891, 1.113,0.550,"",0,30])
                self.navigation_complete_after_next = True  # 设置完成标志

            return True

        except Exception as e:
            rospy.logerr("红绿灯检测时出错: %s", str(e))
            rospy.logerr(traceback.format_exc())
            print(f"【错误】: 红绿灯检测失败: {str(e)}")
            return False

    # 添加更多的功能方法...

def main():
    try:
        # 创建导航对象
        navigator = WaypointNavigation()

        # 检查move_base服务器是否可用
        if navigator.client is None:
            rospy.logwarn("move_base客户端未初始化，但将继续执行程序")
            # 不启动任何其他程序，直接继续
        elif not navigator.client.wait_for_server(rospy.Duration(3.0)):
            rospy.logwarn("move_base服务器不可用，但将继续执行程序")
            # 不启动任何其他程序，直接继续
        else:
            rospy.loginfo("move_base服务器已准备就绪")

        # 设置初始位置（使用我们之前设置的起点位置）
        navigator.set_initial_pose(-11.272, 1.036, 2.482)

        # 等待一下，确保初始位置已经生效
        rospy.sleep(2.0)

        # 不需要等待用户确认，直接开始导航
        rospy.loginfo("自动开始导航...")

        # 导航所有点
        navigator.navigate_all_waypoints()

        rospy.loginfo("导航任务完成")

    except rospy.ROSInterruptException:
        rospy.loginfo("导航被中断")
    except Exception as e:
        rospy.logerr("发生错误: %s" % str(e))

if __name__ == '__main__':
    main()
