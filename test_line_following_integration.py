#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import subprocess

class LineFollowingIntegrationTester:
    """线跟踪集成功能测试"""
    
    def __init__(self):
        # 导航点配置（与实际脚本一致）
        self.waypoints_array = [
            [-12.6, 1.45, -0.682, ["detect_white_line", "scan_qrcode"], 0, 30],  # 索引0: 点1
            [-14.1, -0.8, -0.682, ["rotate_360_detect_banana"], 0, 60],          # 索引1: 点2
            [-15.922, -0.447, -2.2, "detect_traffic_light", 0, 30],              # 索引2: 红绿灯1
            [-16.884, 0.516, -2.2, "detect_traffic_light", 0, 30],               # 索引3: 红绿灯2
            [-15.400, -0.100, 1.098, "", 0, 30],                                 # 索引4: 路口1
            [-16.891, 1.113, 0.550, "", 0, 30],                                  # 索引5: 路口2
        ]
        
        # 音频文件
        self.audio_files = {
            "路口1": "/home/<USER>/ucar_ws/my_code/voice/路口1.wav",
            "路口2": "/home/<USER>/ucar_ws/my_code/voice/路口2.wav"
        }
        
        # 线跟踪脚本路径
        self.line_following_script = "/home/<USER>/ucar_ws/my_code/run_line_following.sh"
        
        # 模拟导航状态
        self.next_waypoint = 0
        self.navigation_complete_after_next = False
        self.current_waypoint = 0
        self.traffic_light_1_is_red = False
        
        # 目标位置音频播放控制
        self.should_play_audio_at_destination = False
        self.destination_audio_file = ""
        self.destination_audio_description = ""
    
    def play_audio_file(self, audio_file_path, description="音频"):
        """播放指定的音频文件"""
        print(f"🔊 播放{description}音频: {audio_file_path}")
        
        if os.path.exists(audio_file_path):
            try:
                result = os.system(f"aplay {audio_file_path}")
                if result == 0:
                    print(f"✅ {description}音频播放完成")
                else:
                    print(f"❌ {description}音频播放失败，返回码: {result}")
            except Exception as e:
                print(f"❌ 播放{description}音频失败: {str(e)}")
        else:
            print(f"❌ {description}音频文件不存在: {audio_file_path}")
    
    def run_line_following_script(self, intersection_name):
        """运行线跟踪脚本（模拟版本）"""
        print(f"【线跟踪】: 在{intersection_name}启动线跟踪程序")
        
        # 线跟踪脚本路径
        script_path = self.line_following_script
        
        # 检查脚本是否存在
        if os.path.exists(script_path):
            try:
                print(f"【线跟踪】: 执行脚本: {script_path}")
                print(f"【线跟踪】: 正在启动线跟踪程序...")
                print(f"【线跟踪】: 脚本将在后台运行")
                print(f"【线跟踪】: 如需停止，请手动终止线跟踪程序")
                
                # 模拟启动脚本（实际环境中会真正启动）
                print(f"【线跟踪】: 模拟启动线跟踪脚本...")
                print(f"【线跟踪】: 在实际环境中，这里会执行:")
                print(f"【线跟踪】:   subprocess.Popen(['{script_path}'], shell=True)")
                print(f"【线跟踪】: 导航任务完成，线跟踪程序接管控制")
                
                # 检查脚本权限
                if os.access(script_path, os.X_OK):
                    print(f"【线跟踪】: ✅ 脚本有执行权限")
                else:
                    print(f"【线跟踪】: ❌ 脚本没有执行权限")
                
            except Exception as e:
                print(f"【线跟踪】: 启动失败 - {str(e)}")
        else:
            print(f"【线跟踪】: 脚本文件不存在 - {script_path}")
    
    def simulate_navigate_to_waypoint(self, index):
        """模拟导航到指定导航点"""
        waypoint_names = ["点1", "点2", "红绿灯1", "红绿灯2", "路口1", "路口2"]
        
        print(f"\n🚗 导航到{waypoint_names[index]} (索引{index})")
        print(f"   坐标: {self.waypoints_array[index][:3]}")
        
        # 模拟导航完成
        print("   导航完成")
        
        # 检查是否需要在此位置播放音频
        if self.should_play_audio_at_destination:
            print("\n" + "="*50)
            print("【目标位置音频】: 到达目标位置，播放音频")
            print("="*50 + "\n")
            
            # 播放音频
            self.play_audio_file(self.destination_audio_file, self.destination_audio_description)
            
            # 重置音频播放标志
            self.should_play_audio_at_destination = False
            self.destination_audio_file = ""
            self.destination_audio_description = ""
        
        # 检查是否到达路口1或路口2，如果是则运行线跟踪脚本
        if index == 4 or index == 5:  # 索引4是路口1，索引5是路口2
            intersection_name = "路口1" if index == 4 else "路口2"
            print("\n" + "="*50)
            print("【线跟踪启动】: 到达%s，启动线跟踪程序" % intersection_name)
            print("="*50 + "\n")
            
            # 运行线跟踪脚本
            self.run_line_following_script(intersection_name)
        
        # 执行动作（如果有）
        actions = self.waypoints_array[index][3] if len(self.waypoints_array[index]) > 3 else []
        if actions:
            if isinstance(actions, list):
                for action in actions:
                    print(f"🔧 执行动作: {action}")
            else:
                print(f"🔧 执行动作: {actions}")
    
    def simulate_scenario_green_light(self):
        """模拟场景：红绿灯1是绿灯"""
        print(f"\n{'='*60}")
        print(f"🎬 场景1：红绿灯1是绿灯 → 路口1 → 线跟踪")
        print(f"{'='*60}")
        
        # 重置状态
        self.reset_state()
        
        # 模拟红绿灯1是绿灯的设置
        self.should_play_audio_at_destination = True
        self.destination_audio_file = self.audio_files["路口1"]
        self.destination_audio_description = "路口1"
        
        # 导航到路口1（应该播放音频并启动线跟踪）
        print(f"\n📍 导航到路口1")
        self.current_waypoint = 4
        self.simulate_navigate_to_waypoint(4)
        
        print(f"\n✅ 场景1完成：路口1 → 播放音频 → 启动线跟踪")
        print(f"{'='*60}")
    
    def simulate_scenario_red_light(self):
        """模拟场景：红绿灯1是红灯"""
        print(f"\n{'='*60}")
        print(f"🎬 场景2：红绿灯1是红灯 → 路口2 → 线跟踪")
        print(f"{'='*60}")
        
        # 重置状态
        self.reset_state()
        
        # 模拟红绿灯1是红灯，已经在红绿灯2播放了音频
        print(f"\n📍 模拟：已在红绿灯2播放路口2音频")
        self.play_audio_file(self.audio_files["路口2"], "路口2")
        
        # 导航到路口2（应该启动线跟踪，但不播放音频）
        print(f"\n📍 导航到路口2")
        self.current_waypoint = 5
        self.simulate_navigate_to_waypoint(5)
        
        print(f"\n✅ 场景2完成：路口2 → 启动线跟踪")
        print(f"{'='*60}")
    
    def reset_state(self):
        """重置状态"""
        self.next_waypoint = 0
        self.navigation_complete_after_next = False
        self.current_waypoint = 0
        self.traffic_light_1_is_red = False
        self.should_play_audio_at_destination = False
        self.destination_audio_file = ""
        self.destination_audio_description = ""
    
    def check_line_following_script(self):
        """检查线跟踪脚本状态"""
        print("\n🔍 检查线跟踪脚本状态:")
        print("-" * 50)
        
        script_path = self.line_following_script
        
        # 检查文件是否存在
        if os.path.exists(script_path):
            print(f"✅ 脚本文件存在: {script_path}")
            
            # 检查执行权限
            if os.access(script_path, os.X_OK):
                print(f"✅ 脚本有执行权限")
            else:
                print(f"❌ 脚本没有执行权限")
                print(f"   可以运行: chmod +x {script_path}")
            
            # 检查文件大小
            file_size = os.path.getsize(script_path)
            print(f"📊 脚本文件大小: {file_size} 字节")
            
            # 检查脚本内容（前几行）
            try:
                with open(script_path, 'r') as f:
                    first_lines = [f.readline().strip() for _ in range(3)]
                print(f"📄 脚本前3行:")
                for i, line in enumerate(first_lines, 1):
                    if line:
                        print(f"   {i}: {line}")
            except Exception as e:
                print(f"❌ 读取脚本内容失败: {e}")
                
        else:
            print(f"❌ 脚本文件不存在: {script_path}")
        
        print("-" * 50)
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 线跟踪集成功能测试")
        
        # 显示导航点配置
        print("\n📍 导航点配置:")
        print("-" * 60)
        waypoint_names = ["点1", "点2", "红绿灯1", "红绿灯2", "路口1", "路口2"]
        for i, (waypoint, name) in enumerate(zip(self.waypoints_array, waypoint_names)):
            actions = waypoint[3] if len(waypoint) > 3 and waypoint[3] else "无"
            line_following = " → 🚗线跟踪" if i in [4, 5] else ""
            print(f"索引{i}: {name} - {waypoint[:3]} - 动作: {actions}{line_following}")
        print("-" * 60)
        
        # 检查线跟踪脚本
        self.check_line_following_script()
        
        # 检查音频文件
        print("\n🎵 音频文件检查:")
        for name, path in self.audio_files.items():
            status = "✅ 存在" if os.path.exists(path) else "❌ 不存在"
            print(f"  {name}: {status}")
        
        print("\n📝 集成后的完整流程:")
        print("   ✅ 红绿灯1是绿灯 → 路口1播放音频 → 启动线跟踪")
        print("   ✅ 红绿灯1是红灯 → 红绿灯2播放音频 → 路口2启动线跟踪")
        
        # 测试场景1: 红绿灯1是绿灯
        input("\n按回车键开始场景1测试（红绿灯1是绿灯 → 路口1 → 线跟踪）...")
        self.simulate_scenario_green_light()
        
        # 测试场景2: 红绿灯1是红灯
        input("\n按回车键开始场景2测试（红绿灯1是红灯 → 路口2 → 线跟踪）...")
        self.simulate_scenario_red_light()
        
        print("\n🎉 测试完成！")
        print("\n📝 集成功能总结:")
        print("   ✅ 到达路口1或路口2后自动播放相应音频")
        print("   ✅ 播放音频后自动启动线跟踪程序")
        print("   ✅ 导航任务完成，线跟踪程序接管车辆控制")
        print("   ✅ 实现了从导航到线跟踪的无缝切换")

if __name__ == "__main__":
    tester = LineFollowingIntegrationTester()
    tester.run_test()
