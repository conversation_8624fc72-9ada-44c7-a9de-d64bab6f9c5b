#!/usr/bin/env python
# -*- coding: utf-8 -*-

import ast
import os

def check_syntax_fix():
    """检查语法修复"""
    script_path = "/home/<USER>/ucar_ws/my_code/waypoint_navigation_array.py"
    
    print("🔍 检查语法修复...")
    print(f"📁 文件: {script_path}")
    
    if not os.path.exists(script_path):
        print("❌ 文件不存在")
        return False
    
    try:
        # 读取文件内容
        with open(script_path, 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # 解析AST
        tree = ast.parse(source_code, filename=script_path)
        print("✅ Python语法检查通过")
        
        # 检查run_line_following_script函数
        print("\n🔍 检查run_line_following_script函数...")
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name == 'run_line_following_script':
                print("✅ 找到run_line_following_script函数")
                
                # 检查函数内的import语句
                imports_found = []
                for child in ast.walk(node):
                    if isinstance(child, ast.Import):
                        for alias in child.names:
                            imports_found.append(alias.name)
                    elif isinstance(child, ast.ImportFrom):
                        imports_found.append(f"from {child.module}")
                
                print(f"📦 函数内导入的模块: {imports_found}")
                
                if 'os' in imports_found and 'subprocess' in imports_found:
                    print("✅ os和subprocess模块正确导入")
                    return True
                else:
                    print("❌ 缺少必要的模块导入")
                    return False
        
        print("❌ 未找到run_line_following_script函数")
        return False
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        print(f"   列号: {e.offset}")
        print(f"   错误内容: {e.text}")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_specific_fix():
    """检查具体的修复内容"""
    script_path = "/home/<USER>/ucar_ws/my_code/waypoint_navigation_array.py"
    
    print("\n🔍 检查具体修复内容...")
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找run_line_following_script函数
        function_start = None
        for i, line in enumerate(lines):
            if 'def run_line_following_script(self, intersection_name):' in line:
                function_start = i
                break
        
        if function_start is None:
            print("❌ 未找到run_line_following_script函数")
            return False
        
        print(f"✅ 找到函数，起始行: {function_start + 1}")
        
        # 检查函数开始后的几行
        function_lines = lines[function_start:function_start + 10]
        
        print("\n📝 函数前10行:")
        for i, line in enumerate(function_lines):
            print(f"   {function_start + i + 1:3d}: {line.rstrip()}")
        
        # 检查是否有正确的导入
        has_os_import = False
        has_subprocess_import = False
        
        for line in function_lines:
            if 'import os' in line:
                has_os_import = True
            if 'import subprocess' in line:
                has_subprocess_import = True
        
        if has_os_import and has_subprocess_import:
            print("\n✅ 修复成功：os和subprocess模块在函数开始处正确导入")
            return True
        else:
            print(f"\n❌ 修复不完整:")
            print(f"   os导入: {'✅' if has_os_import else '❌'}")
            print(f"   subprocess导入: {'✅' if has_subprocess_import else '❌'}")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    print("🚀 语法修复验证")
    print("=" * 50)
    
    # 1. 语法检查
    syntax_ok = check_syntax_fix()
    
    # 2. 具体修复检查
    fix_ok = check_specific_fix()
    
    print("\n" + "=" * 50)
    if syntax_ok and fix_ok:
        print("🎉 修复验证成功！")
        print("\n📝 修复内容:")
        print("   ✅ 在run_line_following_script函数开始处添加了import os")
        print("   ✅ 在run_line_following_script函数开始处添加了import subprocess")
        print("   ✅ 解决了'local variable 'os' referenced before assignment'错误")
        print("\n🎮 现在可以正常运行导航程序，线跟踪功能将正常工作！")
        return True
    else:
        print("❌ 修复验证失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
