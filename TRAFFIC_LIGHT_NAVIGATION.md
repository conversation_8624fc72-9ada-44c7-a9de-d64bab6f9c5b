# 红绿灯导航音频播放功能实现

## 功能概述

实现了基于红绿灯检测结果的智能导航和音频播放功能：

- **红绿灯1是绿灯** → 播放"路口1.wav" → 导航到路口1
- **红绿灯1是红灯** → 导航到红绿灯2 → 播放"路口2.wav" → 导航到路口2

## 导航点配置

```python
self.waypoints_array = [
    [-12.6, 1.45, -0.682, ["detect_white_line", "scan_qrcode"], 0, 30],  # 点1
    [-14.1, -0.8, -0.682, ["rotate_360_detect_banana"], 0, 60],          # 点2
    [-15.922, -0.447, -2.2, "detect_traffic_light", 0, 30],              # 红绿灯1 (索引2)
    [-16.884, 0.516, -2.2, "detect_traffic_light", 0, 30],               # 红绿灯2 (索引3)
    [-15.400, -0.100, 1.098, "", 0, 30],                                 # 路口1 (索引4)
    [-16.891, 1.113, 0.550, "", 0, 30],                                  # 路口2 (索引5)
]
```

## 音频文件

| 场景 | 音频文件路径 |
|------|-------------|
| 红绿灯1是绿灯 | `/home/<USER>/ucar_ws/my_code/voice/路口1.wav` |
| 到达红绿灯2 | `/home/<USER>/ucar_ws/my_code/voice/路口2.wav` |

## 导航逻辑流程

### 场景1：红绿灯1是绿灯 🟢
```
到达红绿灯1 → 检测到绿灯 → 播放"路口1.wav" → 导航到路口1 → 结束导航
```

### 场景2：红绿灯1是红灯 🔴
```
到达红绿灯1 → 检测到红灯 → 导航到红绿灯2 → 播放"路口2.wav" → 导航到路口2 → 结束导航
```

## 实现细节

### 1. 修改的函数

#### `detect_traffic_light()` 函数
- 增加了音频播放逻辑
- 根据红绿灯状态设置正确的导航点索引

#### 新增 `play_audio_file()` 函数
```python
def play_audio_file(self, audio_file_path, description="音频"):
    """播放指定的音频文件"""
    # 检查文件存在性
    # 使用aplay播放WAV文件
    # 错误处理和日志记录
```

### 2. 导航点索引映射

| 导航点 | 索引 | 坐标 | 说明 |
|--------|------|------|------|
| 红绿灯1 | 2 | [-15.922, -0.447, -2.2] | 第一个红绿灯检测点 |
| 红绿灯2 | 3 | [-16.884, 0.516, -2.2] | 第二个红绿灯检测点 |
| 路口1 | 4 | [-15.400, -0.100, 1.098] | 绿灯1的目标路口 |
| 路口2 | 5 | [-16.891, 1.113, 0.550] | 红绿灯2的目标路口 |

### 3. 关键代码片段

```python
# 红绿灯1检测逻辑
if traffic_light_number == 1:  # 红绿灯1
    if is_green:
        # 绿灯：播放路口1音频，导航到路口1
        self.play_audio_file("/home/<USER>/ucar_ws/my_code/voice/路口1.wav", "路口1")
        self.next_waypoint = 4  # 路口1
        self.navigation_complete_after_next = True
    else:
        # 红灯：继续到红绿灯2
        self.next_waypoint = 3  # 红绿灯2

# 红绿灯2检测逻辑
elif traffic_light_number == 2:  # 红绿灯2
    # 播放路口2音频，导航到路口2
    self.play_audio_file("/home/<USER>/ucar_ws/my_code/voice/路口2.wav", "路口2")
    self.next_waypoint = 5  # 路口2
    self.navigation_complete_after_next = True
```

## 测试验证

### 1. 音频播放测试
```bash
# 测试路口1音频
python3 /home/<USER>/ucar_ws/test_traffic_light_audio.py 路口1

# 测试路口2音频
python3 /home/<USER>/ucar_ws/test_traffic_light_audio.py 路口2

# 测试所有场景
python3 /home/<USER>/ucar_ws/test_traffic_light_audio.py
```

### 2. 完整导航测试
```bash
# 运行完整导航脚本
/home/<USER>/ucar_ws/my_code/run_waypoint_fixed.sh
```

## 日志输出示例

### 红绿灯1是绿灯的情况：
```
【红绿灯检测】: 检测结果 = True
【决策】: 红绿灯1是绿灯，播放路口1音频并导航到路口1
【音频播放】: 播放路口1音频: /home/<USER>/ucar_ws/my_code/voice/路口1.wav
Playing WAVE '/home/<USER>/ucar_ws/my_code/voice/路口1.wav' : Signed 16 bit Little Endian, Rate 16000 Hz, Mono
【音频播放】: 路口1音频播放完成
【导航】: 将导航到路口1并结束导航
```

### 红绿灯1是红灯的情况：
```
【红绿灯检测】: 检测结果 = False
【决策】: 红绿灯1是红灯，将继续检测红绿灯2
【导航】: 将导航到红绿灯2
...
【决策】: 到达红绿灯2，播放路口2音频并导航到路口2
【音频播放】: 播放路口2音频: /home/<USER>/ucar_ws/my_code/voice/路口2.wav
Playing WAVE '/home/<USER>/ucar_ws/my_code/voice/路口2.wav' : Signed 16 bit Little Endian, Rate 16000 Hz, Mono
【音频播放】: 路口2音频播放完成
【导航】: 将导航到路口2并结束导航
```

## 注意事项

1. **音频文件格式**：确保WAV文件格式正确（16kHz采样率，单声道）
2. **文件路径**：确保音频文件路径正确且文件存在
3. **音频设备**：确保系统音频设备正常，`aplay` 命令可用
4. **导航点索引**：确保导航点索引与实际数组索引匹配
5. **红绿灯检测**：确保YOLO模型能正确检测红绿灯状态
