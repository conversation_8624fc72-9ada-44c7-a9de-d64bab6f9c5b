#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os

class FinalTrafficLightFixTester:
    """最终修复的红绿灯逻辑测试"""
    
    def __init__(self):
        # 导航点配置（与实际脚本一致）
        self.waypoints_array = [
            [-12.6, 1.45, -0.682, ["detect_white_line", "scan_qrcode"], 0, 30],  # 索引0: 点1
            [-14.1, -0.8, -0.682, ["rotate_360_detect_banana"], 0, 60],          # 索引1: 点2
            [-15.922, -0.447, -2.2, "detect_traffic_light", 0, 30],              # 索引2: 红绿灯1
            [-16.884, 0.516, -2.2, "detect_traffic_light", 0, 30],               # 索引3: 红绿灯2
            [-15.400, -0.100, 1.098, "", 0, 30],                                 # 索引4: 路口1
            [-16.891, 1.113, 0.550, "", 0, 30],                                  # 索引5: 路口2
        ]
        
        # 音频文件
        self.audio_files = {
            "路口1": "/home/<USER>/ucar_ws/my_code/voice/路口1.wav",
            "路口2": "/home/<USER>/ucar_ws/my_code/voice/路口2.wav"
        }
        
        # 模拟导航状态
        self.next_waypoint = 0
        self.navigation_complete_after_next = False
        self.current_waypoint = 0
        self.traffic_light_1_is_red = False  # 红绿灯1是否为红灯
    
    def play_audio_file(self, audio_file_path, description="音频"):
        """播放指定的音频文件"""
        print(f"🔊 播放{description}音频: {audio_file_path}")
        
        if os.path.exists(audio_file_path):
            try:
                result = os.system(f"aplay {audio_file_path}")
                if result == 0:
                    print(f"✅ {description}音频播放完成")
                else:
                    print(f"❌ {description}音频播放失败，返回码: {result}")
            except Exception as e:
                print(f"❌ 播放{description}音频失败: {str(e)}")
        else:
            print(f"❌ {description}音频文件不存在: {audio_file_path}")
    
    def get_traffic_light_number(self, current_waypoint_index):
        """根据导航点索引确定红绿灯编号（修复后的逻辑）"""
        if current_waypoint_index == 2:  # 索引2是红绿灯1
            return 1
        elif current_waypoint_index == 3:  # 索引3是红绿灯2
            return 2
        else:
            return current_waypoint_index + 1  # 默认逻辑
    
    def simulate_execute_action(self, action):
        """模拟execute_action函数的红绿灯检测部分（修复后）"""
        if action == "detect_traffic_light":
            print(f"\n🔧 执行动作: {action}")
            print(f"📍 当前导航点索引: {self.current_waypoint}")
            print(f"🚦 红绿灯1是红灯标志: {self.traffic_light_1_is_red}")
            
            # 检查当前是否在红绿灯2位置且红绿灯1是红灯
            if self.current_waypoint == 3 and self.traffic_light_1_is_red:  # 索引3是红绿灯2
                # 红绿灯1是红灯时，红绿灯2位置不检测，直接播放音频并导航到路口2
                print("✅ 条件匹配：红绿灯2位置 + 红绿灯1是红灯")
                print("红绿灯1是红灯，红绿灯2位置跳过检测，直接播放路口2音频并导航到路口2")
                print("\n" + "="*50)
                print("【红绿灯2逻辑】: 红绿灯1是红灯，跳过红绿灯2检测")
                print("【决策】: 直接播放路口2音频并导航到路口2")
                print("="*50 + "\n")
                
                # 播放路口2音频
                self.play_audio_file(self.audio_files["路口2"], "路口2")
                
                # 设置下一个导航点为路口2
                self.next_waypoint = 5  # 索引5对应路口2
                self.navigation_complete_after_next = True  # 设置完成标志
                
                print("\n" + "="*50)
                print(f"【导航设置】: 下一个导航点 = {self.next_waypoint} (路口2)")
                print(f"【完成标志】: {'是' if self.navigation_complete_after_next else '否'}")
                print("="*50 + "\n")
            else:
                # 正常的红绿灯检测逻辑
                print("❌ 条件不匹配，执行正常红绿灯检测")
                print(f"   - 当前导航点: {self.current_waypoint} (需要是3)")
                print(f"   - 红绿灯1是红灯: {self.traffic_light_1_is_red} (需要是True)")
                self.simulate_detect_traffic_light()
    
    def simulate_detect_traffic_light(self):
        """模拟detect_traffic_light函数（修复后的逻辑）"""
        # 获取当前导航点索引，用于确定是哪个红绿灯
        current_waypoint_index = self.current_waypoint
        traffic_light_number = self.get_traffic_light_number(current_waypoint_index)
        
        print(f"\n🚦 红绿灯检测")
        print(f"📍 当前导航点索引: {current_waypoint_index}")
        print(f"🚦 红绿灯编号: {traffic_light_number}")
        
        # 模拟检测结果
        if traffic_light_number == 1:
            # 模拟红绿灯1检测到红灯
            is_green = False
            print(f"检测结果: {'绿灯🟢' if is_green else '红灯🔴'}")
            
            if is_green:
                # 如果红绿灯1是绿灯，播放路口1音频，然后导航到路口1
                print("【决策】: 红绿灯1是绿灯，播放路口1音频并导航到路口1")
                
                # 播放路口1音频
                self.play_audio_file(self.audio_files["路口1"], "路口1")
                
                # 设置下一个导航点为路口1
                self.next_waypoint = 4  # 索引4对应路口1
                self.navigation_complete_after_next = True  # 设置完成标志
            else:
                # 如果红绿灯1不是绿灯（红灯），继续检测红绿灯2
                self.traffic_light_1_is_red = True  # 设置红绿灯1是红灯的标志
                self.next_waypoint = 3  # 索引3对应红绿灯2
                print("【决策】: 红绿灯1是红灯，将继续到红绿灯2位置")
                print("【状态】: 设置红绿灯1是红灯标志，红绿灯2将跳过检测")
                print(f"🏁 设置红绿灯1是红灯标志: {self.traffic_light_1_is_red}")
        
        elif traffic_light_number == 2:
            # 这个分支在修复后应该不会被执行，因为会在execute_action中被拦截
            print("⚠️  警告：这个分支不应该被执行！")
            print("【说明】: 红绿灯2应该在execute_action中被拦截")
    
    def simulate_complete_scenario(self):
        """模拟完整的红绿灯1是红灯的场景"""
        print(f"\n{'='*60}")
        print(f"🎬 完整场景测试：红绿灯1是红灯")
        print(f"{'='*60}")
        
        # 重置状态
        self.next_waypoint = 0
        self.navigation_complete_after_next = False
        self.current_waypoint = 0
        self.traffic_light_1_is_red = False
        
        waypoint_names = ["点1", "点2", "红绿灯1", "红绿灯2", "路口1", "路口2"]
        
        # 步骤1: 导航到红绿灯1
        print(f"\n📍 步骤1: 导航到红绿灯1")
        self.current_waypoint = 2  # 红绿灯1的索引
        print(f"   当前导航点: {waypoint_names[self.current_waypoint]} (索引{self.current_waypoint})")
        
        # 步骤2: 执行红绿灯1检测
        print(f"\n🔧 步骤2: 执行红绿灯1检测")
        self.simulate_execute_action("detect_traffic_light")
        
        # 步骤3: 导航到红绿灯2
        if self.traffic_light_1_is_red:
            print(f"\n📍 步骤3: 导航到红绿灯2")
            self.current_waypoint = 3  # 红绿灯2的索引
            print(f"   当前导航点: {waypoint_names[self.current_waypoint]} (索引{self.current_waypoint})")
            print(f"   红绿灯1是红灯标志: {self.traffic_light_1_is_red}")
            
            # 步骤4: 执行红绿灯2的动作（应该跳过检测）
            print(f"\n🔧 步骤4: 执行红绿灯2的动作")
            self.simulate_execute_action("detect_traffic_light")
            
            # 步骤5: 导航到路口2
            if self.next_waypoint == 5:
                print(f"\n📍 步骤5: 导航到路口2")
                print(f"   目标导航点: {waypoint_names[self.next_waypoint]} (索引{self.next_waypoint})")
                print("🏁 导航结束")
            else:
                print(f"\n❌ 错误：下一个导航点不是路口2，而是索引{self.next_waypoint}")
        else:
            print(f"\n❌ 错误：红绿灯1不是红灯，测试失败")
        
        print(f"\n{'='*60}")
        print(f"✅ 完整场景测试完成")
        print(f"{'='*60}")
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 最终修复的红绿灯导航逻辑测试")
        
        # 显示导航点配置
        print("\n📍 导航点配置:")
        print("-" * 60)
        waypoint_names = ["点1", "点2", "红绿灯1", "红绿灯2", "路口1", "路口2"]
        for i, (waypoint, name) in enumerate(zip(self.waypoints_array, waypoint_names)):
            actions = waypoint[3] if len(waypoint) > 3 and waypoint[3] else "无"
            print(f"索引{i}: {name} - {waypoint[:3]} - 动作: {actions}")
        print("-" * 60)
        
        # 检查音频文件
        print("\n🎵 音频文件检查:")
        for name, path in self.audio_files.items():
            status = "✅ 存在" if os.path.exists(path) else "❌ 不存在"
            print(f"  {name}: {status}")
        
        # 测试红绿灯编号映射
        print("\n🔢 测试红绿灯编号映射:")
        for i in range(6):
            traffic_light_number = self.get_traffic_light_number(i)
            print(f"  导航点索引{i} → 红绿灯{traffic_light_number}")
        
        # 运行完整场景测试
        input("\n按回车键开始完整场景测试...")
        self.simulate_complete_scenario()
        
        print("\n🎉 测试完成！")
        print("\n📝 修复总结:")
        print("   ✅ 修复了红绿灯编号映射问题")
        print("   ✅ 红绿灯1是红灯时，红绿灯2位置跳过检测")
        print("   ✅ 直接播放路口2音频并导航到路口2")
        print("   ✅ 不会错误地导航到路口1")

if __name__ == "__main__":
    tester = FinalTrafficLightFixTester()
    tester.run_test()
