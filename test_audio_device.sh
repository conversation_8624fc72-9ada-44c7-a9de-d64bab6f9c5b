#!/bin/bash
# 测试音频设备是否正常工作

echo "===== 测试音频设备 ====="

# 检查音频设备
echo "检查音频设备列表:"
arecord -l

echo ""
echo "测试音频设备 hw:3,0 是否可用:"
timeout 3 arecord -D hw:3,0 -f S16_LE -r 16000 -c 1 /tmp/test_audio.wav
if [ $? -eq 0 ]; then
    echo "音频设备 hw:3,0 工作正常"
    rm -f /tmp/test_audio.wav
else
    echo "音频设备 hw:3,0 可能有问题"
fi

echo ""
echo "测试音频设备 hw:XFMDPV0018 是否可用:"
timeout 3 arecord -D hw:XFMDPV0018 -f S16_LE -r 16000 -c 1 /tmp/test_audio2.wav
if [ $? -eq 0 ]; then
    echo "音频设备 hw:XFMDPV0018 工作正常"
    rm -f /tmp/test_audio2.wav
else
    echo "音频设备 hw:XFMDPV0018 可能有问题"
fi

echo "音频设备测试完成"
