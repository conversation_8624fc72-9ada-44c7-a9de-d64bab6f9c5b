#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time

class TrafficLightNavigationDemo:
    """红绿灯导航功能演示"""
    
    def __init__(self):
        self.audio_files = {
            "路口1": "/home/<USER>/ucar_ws/my_code/voice/路口1.wav",
            "路口2": "/home/<USER>/ucar_ws/my_code/voice/路口2.wav"
        }
        
        # 导航点配置
        self.waypoints = [
            {"name": "点1", "coord": "[-12.6, 1.45, -0.682]", "actions": ["detect_white_line", "scan_qrcode"]},
            {"name": "点2", "coord": "[-14.1, -0.8, -0.682]", "actions": ["rotate_360_detect_banana"]},
            {"name": "红绿灯1", "coord": "[-15.922, -0.447, -2.2]", "actions": ["detect_traffic_light"]},
            {"name": "红绿灯2", "coord": "[-16.884, 0.516, -2.2]", "actions": ["detect_traffic_light"]},
            {"name": "路口1", "coord": "[-15.400, -0.100, 1.098]", "actions": []},
            {"name": "路口2", "coord": "[-16.891, 1.113, 0.550]", "actions": []}
        ]
    
    def play_audio_file(self, audio_file_path, description="音频"):
        """播放指定的音频文件"""
        print(f"🔊 播放{description}音频: {audio_file_path}")
        
        if os.path.exists(audio_file_path):
            try:
                result = os.system(f"aplay {audio_file_path}")
                if result == 0:
                    print(f"✅ {description}音频播放完成")
                else:
                    print(f"❌ {description}音频播放失败，返回码: {result}")
            except Exception as e:
                print(f"❌ 播放{description}音频失败: {str(e)}")
        else:
            print(f"❌ {description}音频文件不存在: {audio_file_path}")
    
    def simulate_navigation_to_waypoint(self, waypoint_name, coord):
        """模拟导航到指定点"""
        print(f"🚗 导航到{waypoint_name} {coord}")
        print("   导航中...")
        time.sleep(1)  # 模拟导航时间
        print(f"✅ 已到达{waypoint_name}")
    
    def simulate_traffic_light_detection(self, traffic_light_number, is_green):
        """模拟红绿灯检测"""
        print(f"\n🚦 检测红绿灯{traffic_light_number}...")
        time.sleep(0.5)
        
        light_status = "绿灯🟢" if is_green else "红灯🔴"
        print(f"📊 检测结果: {light_status}")
        
        return is_green
    
    def demo_scenario_1(self):
        """演示场景1：红绿灯1是绿灯"""
        print("\n" + "="*60)
        print("🎬 场景1演示：红绿灯1是绿灯")
        print("="*60)
        
        # 导航到前面的点
        for i in range(3):  # 点1, 点2, 红绿灯1
            waypoint = self.waypoints[i]
            self.simulate_navigation_to_waypoint(waypoint["name"], waypoint["coord"])
            
            if waypoint["actions"]:
                print(f"🔧 执行动作: {waypoint['actions']}")
                time.sleep(0.5)
        
        # 检测红绿灯1
        is_green = self.simulate_traffic_light_detection(1, True)  # 绿灯
        
        if is_green:
            print("\n💡 决策：红绿灯1是绿灯，播放路口1音频并导航到路口1")
            
            # 播放路口1音频
            self.play_audio_file(self.audio_files["路口1"], "路口1")
            
            # 导航到路口1
            waypoint = self.waypoints[4]  # 路口1
            self.simulate_navigation_to_waypoint(waypoint["name"], waypoint["coord"])
            
            print("🏁 导航结束")
    
    def demo_scenario_2(self):
        """演示场景2：红绿灯1是红灯"""
        print("\n" + "="*60)
        print("🎬 场景2演示：红绿灯1是红灯")
        print("="*60)
        
        # 导航到前面的点
        for i in range(3):  # 点1, 点2, 红绿灯1
            waypoint = self.waypoints[i]
            self.simulate_navigation_to_waypoint(waypoint["name"], waypoint["coord"])
            
            if waypoint["actions"]:
                print(f"🔧 执行动作: {waypoint['actions']}")
                time.sleep(0.5)
        
        # 检测红绿灯1
        is_green = self.simulate_traffic_light_detection(1, False)  # 红灯
        
        if not is_green:
            print("\n💡 决策：红绿灯1是红灯，继续导航到红绿灯2")
            
            # 导航到红绿灯2
            waypoint = self.waypoints[3]  # 红绿灯2
            self.simulate_navigation_to_waypoint(waypoint["name"], waypoint["coord"])
            
            # 检测红绿灯2（总是执行路口2逻辑）
            self.simulate_traffic_light_detection(2, True)
            
            print("\n💡 决策：到达红绿灯2，播放路口2音频并导航到路口2")
            
            # 播放路口2音频
            self.play_audio_file(self.audio_files["路口2"], "路口2")
            
            # 导航到路口2
            waypoint = self.waypoints[5]  # 路口2
            self.simulate_navigation_to_waypoint(waypoint["name"], waypoint["coord"])
            
            print("🏁 导航结束")
    
    def show_waypoints_info(self):
        """显示导航点信息"""
        print("\n📍 导航点配置:")
        print("-" * 50)
        for i, waypoint in enumerate(self.waypoints):
            actions_str = ", ".join(waypoint["actions"]) if waypoint["actions"] else "无"
            print(f"{i}. {waypoint['name']}: {waypoint['coord']}")
            print(f"   动作: {actions_str}")
        print("-" * 50)
    
    def show_audio_files_info(self):
        """显示音频文件信息"""
        print("\n🎵 音频文件配置:")
        print("-" * 50)
        for name, path in self.audio_files.items():
            status = "✅ 存在" if os.path.exists(path) else "❌ 不存在"
            print(f"{name}: {path} ({status})")
        print("-" * 50)
    
    def run_demo(self):
        """运行完整演示"""
        print("🚀 红绿灯导航音频播放功能演示")
        print("="*60)
        
        # 显示配置信息
        self.show_waypoints_info()
        self.show_audio_files_info()
        
        # 演示场景1
        input("\n按回车键开始场景1演示（红绿灯1是绿灯）...")
        self.demo_scenario_1()
        
        # 演示场景2
        input("\n按回车键开始场景2演示（红绿灯1是红灯）...")
        self.demo_scenario_2()
        
        print("\n🎉 演示完成！")
        print("\n📝 总结:")
        print("   - 红绿灯1是绿灯 → 播放路口1音频 → 导航到路口1")
        print("   - 红绿灯1是红灯 → 导航到红绿灯2 → 播放路口2音频 → 导航到路口2")

if __name__ == "__main__":
    demo = TrafficLightNavigationDemo()
    demo.run_demo()
