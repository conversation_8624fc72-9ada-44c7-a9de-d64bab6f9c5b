#include <ros/ros.h>
#include <std_srvs/Trigger.h>
#include <std_msgs/String.h>
#include <iostream>
#include <cstdlib>

using namespace std;

// 服务回调函数 - 执行导航脚本
bool execute_navigation_script(std_srvs::Trigger::Request &request, std_srvs::Trigger::Response &response)
{
    cout << "收到导航脚本执行请求..." << endl;
    
    // 执行导航脚本
    std::string script_command = "/home/<USER>/ucar_ws/my_code/run_waypoint_fixed.sh";
    cout << "执行命令: " << script_command << endl;
    
    int result = system(script_command.c_str());
    
    if (result == 0) {
        response.success = true;
        response.message = "Navigation script executed successfully";
        cout << "导航脚本执行成功" << endl;
    } else {
        response.success = false;
        response.message = "Failed to execute navigation script";
        cout << "导航脚本执行失败，返回码: " << result << endl;
    }
    
    return true;
}

// 测试服务回调函数
bool test_service(std_srvs::Trigger::Request &request, std_srvs::Trigger::Response &response)
{
    cout << "测试服务被调用" << endl;
    response.success = true;
    response.message = "Test service working";
    return true;
}

int main(int argc, char** argv)
{
    // 初始化ROS节点
    ros::init(argc, argv, "simple_speech_command_node");
    ros::NodeHandle nh;
    
    cout << "简化语音命令节点启动..." << endl;
    
    // 创建服务
    ros::ServiceServer navigation_service = nh.advertiseService("/speech_command_node/execute_navigation", execute_navigation_script);
    ros::ServiceServer test_service_server = nh.advertiseService("/speech_command_node/get_test_video", test_service);
    
    cout << "服务已启动:" << endl;
    cout << "  - /speech_command_node/execute_navigation (执行导航脚本)" << endl;
    cout << "  - /speech_command_node/get_test_video (测试服务)" << endl;
    cout << "节点运行中，等待服务调用..." << endl;
    
    // 保持节点运行
    ros::spin();
    
    return 0;
}
