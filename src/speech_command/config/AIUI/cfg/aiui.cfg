/* AIUI参数配置 */
{
	/* 交互参数 */
	"interact":{
		"interact_timeout":"-1",
		"result_timeout":"2000"
	},

	/* 全局设置 */
	"global":{
		"scene":"main"
	},

	/* 用户自定义参数 */
	"userparams":{
		"k1":"v1",
		"k2":"v2"
	},

	/* 业务相关参数 */
	// 本地vad参数
	"vad":{
		"vad_enable":"1",
		"engine_type":"meta",
		"res_type":"assets",
		"res_path":"/home/<USER>/ucar_ws/src/speech_command/config/AIUI/assets/vad/meta_vad_16k.jet",
		"vad_bos":"1000",
		"vad_eos":"1000",
		"cloud_vad_eos":"30000"
	},

	// 识别（音频输入）参数
	"iat":{
		"engine_type":"local",
		"sample_rate":"16000"
	},

	// 唤醒参数
	"ivw":{
		"res_type":"assets",
		"res_path":"/home/<USER>/ucar_ws/src/speech_command/config/AIUI/ivw/wakeupresource.jet"
	},

	// 离线语法识别参数
	"asr":{
		"threshold":"50",
		"res_type":"assets",
		"res_path":"/home/<USER>/ucar_ws/src/speech_command/config/AIUI/asr/common.jet"
	},

	// 合成参数
	"tts":{
		"engine_type":"local",
		"res_type":"assets",
		"res_path":"/home/<USER>/ucar_ws/src/speech_command/config/AIUI/tts/common.jet",
		"voice_name":"x2_xiaojuan",
		"play_mode": "sdk",
		"buffer_time": "0",     // 音频缓冲时长，当缓冲音频大于该值时才开始播放，默认值：0ms
		"stream_type": "3",     // 播放音频流类型，取值参考AudioManager类，默认值：3
		"audio_focus": "0"      // 播放音频时是否抢占焦点，取值：1, 0（默认值）
	},


	/* 业务流程相关参数 */
	// 语音业务流程
	"speech":{
		"data_source":"user",
		"wakeup_mode":"off",
		"interact_mode":"continuous",
		"intent_engine_type":"local"
	},

	/* 硬件参数设置 */
	// alsa录音参数
	"alsa":{
		"sound_card":"3",
		"card_sample_rate":"96000"
	},

	/* 日志设置 */
	"log":{
		"debug_log":"1",
		"save_datalog":"0",
		"datalog_path":"",
		"datalog_size":1024,
		"raw_audio_path":""
	}

}
