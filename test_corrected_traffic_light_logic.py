#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os

class CorrectedTrafficLightLogicTester:
    """修正后的红绿灯逻辑测试"""
    
    def __init__(self):
        # 导航点配置（与实际脚本一致）
        self.waypoints_array = [
            [-12.6, 1.45, -0.682, ["detect_white_line", "scan_qrcode"], 0, 30],  # 索引0: 点1
            [-14.1, -0.8, -0.682, ["rotate_360_detect_banana"], 0, 60],          # 索引1: 点2
            [-15.922, -0.447, -2.2, "detect_traffic_light", 0, 30],              # 索引2: 红绿灯1
            [-16.884, 0.516, -2.2, "detect_traffic_light", 0, 30],               # 索引3: 红绿灯2
            [-15.400, -0.100, 1.098, "", 0, 30],                                 # 索引4: 路口1
            [-16.891, 1.113, 0.550, "", 0, 30],                                  # 索引5: 路口2
        ]
        
        # 音频文件
        self.audio_files = {
            "路口1": "/home/<USER>/ucar_ws/my_code/voice/路口1.wav",
            "路口2": "/home/<USER>/ucar_ws/my_code/voice/路口2.wav"
        }
        
        # 模拟导航状态
        self.next_waypoint = 0
        self.navigation_complete_after_next = False
    
    def play_audio_file(self, audio_file_path, description="音频"):
        """播放指定的音频文件"""
        print(f"🔊 播放{description}音频: {audio_file_path}")
        
        if os.path.exists(audio_file_path):
            try:
                result = os.system(f"aplay {audio_file_path}")
                if result == 0:
                    print(f"✅ {description}音频播放完成")
                else:
                    print(f"❌ {description}音频播放失败，返回码: {result}")
            except Exception as e:
                print(f"❌ 播放{description}音频失败: {str(e)}")
        else:
            print(f"❌ {description}音频文件不存在: {audio_file_path}")
    
    def simulate_traffic_light_detection(self, traffic_light_number, is_green):
        """模拟红绿灯检测逻辑（修正后的版本）"""
        print(f"\n🚦 模拟红绿灯{traffic_light_number}检测")
        print(f"检测结果: {'绿灯🟢' if is_green else '红灯🔴'}")
        print("-" * 50)
        
        # 根据红绿灯状态和编号设置下一个导航点
        if traffic_light_number == 1:  # 红绿灯1
            if is_green:
                # 如果红绿灯1是绿灯，播放路口1音频，然后导航到路口1，并设置完成标志
                print("【决策】: 红绿灯1是绿灯，播放路口1音频并导航到路口1")
                
                # 播放路口1音频
                self.play_audio_file(self.audio_files["路口1"], "路口1")
                
                # 设置下一个导航点为路口1
                self.next_waypoint = 4  # 索引4对应路口1
                self.navigation_complete_after_next = True  # 设置完成标志
                
                print(f"📍 下一个导航点: 索引{self.next_waypoint} (路口1)")
                print("🏁 导航将在到达路口1后结束")
            else:
                # 如果红绿灯1不是绿灯（红灯），继续检测红绿灯2
                self.next_waypoint = 3  # 索引3对应红绿灯2
                print("【决策】: 红绿灯1是红灯，将继续检测红绿灯2")
                print(f"📍 下一个导航点: 索引{self.next_waypoint} (红绿灯2)")
                
        elif traffic_light_number == 2:  # 红绿灯2
            # 到达红绿灯2，不需要检测红绿灯状态，直接播放路口2音频并导航到路口2
            print("【决策】: 到达红绿灯2位置，播放路口2音频并导航到路口2")
            print("【说明】: 红绿灯1是红灯时的路径，直接前往路口2")
            
            # 播放路口2音频
            self.play_audio_file(self.audio_files["路口2"], "路口2")
            
            # 设置下一个导航点为路口2
            self.next_waypoint = 5  # 索引5对应路口2
            self.navigation_complete_after_next = True  # 设置完成标志
            
            print(f"📍 下一个导航点: 索引{self.next_waypoint} (路口2)")
            print("🏁 导航将在到达路口2后结束")
        
        print("-" * 50)
        return True
    
    def simulate_navigation_path(self, scenario_name, traffic_light_1_status):
        """模拟完整的导航路径"""
        print(f"\n{'='*60}")
        print(f"🎬 {scenario_name}")
        print(f"{'='*60}")
        
        # 重置状态
        self.next_waypoint = 0
        self.navigation_complete_after_next = False
        
        # 模拟导航到前面的点
        waypoint_names = ["点1", "点2", "红绿灯1"]
        for i in range(3):  # 点1, 点2, 红绿灯1
            waypoint = self.waypoints_array[i]
            print(f"\n🚗 导航到{waypoint_names[i]} (索引{i}): {waypoint[:3]}")
            
            if i == 2:  # 到达红绿灯1
                # 执行红绿灯1检测
                self.simulate_traffic_light_detection(1, traffic_light_1_status)
                
                if traffic_light_1_status:  # 绿灯
                    # 直接导航到路口1
                    print(f"\n🚗 导航到路口1 (索引{self.next_waypoint}): {self.waypoints_array[self.next_waypoint][:3]}")
                    print("🏁 导航结束")
                    break
                else:  # 红灯
                    # 继续到红绿灯2
                    print(f"\n🚗 导航到红绿灯2 (索引{self.next_waypoint}): {self.waypoints_array[self.next_waypoint][:3]}")
                    
                    # 执行红绿灯2逻辑
                    self.simulate_traffic_light_detection(2, True)  # 红绿灯2状态不重要
                    
                    # 导航到路口2
                    print(f"\n🚗 导航到路口2 (索引{self.next_waypoint}): {self.waypoints_array[self.next_waypoint][:3]}")
                    print("🏁 导航结束")
                    break
        
        print(f"\n{'='*60}")
        print(f"✅ {scenario_name} 完成")
        print(f"{'='*60}")
    
    def show_waypoints_info(self):
        """显示导航点信息"""
        print("\n📍 导航点配置:")
        print("-" * 60)
        waypoint_names = ["点1", "点2", "红绿灯1", "红绿灯2", "路口1", "路口2"]
        for i, (waypoint, name) in enumerate(zip(self.waypoints_array, waypoint_names)):
            actions = waypoint[3] if len(waypoint) > 3 and waypoint[3] else "无"
            print(f"索引{i}: {name} - {waypoint[:3]} - 动作: {actions}")
        print("-" * 60)
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 修正后的红绿灯导航逻辑测试")
        
        # 显示配置信息
        self.show_waypoints_info()
        
        # 检查音频文件
        print("\n🎵 音频文件检查:")
        for name, path in self.audio_files.items():
            status = "✅ 存在" if os.path.exists(path) else "❌ 不存在"
            print(f"  {name}: {status}")
        
        # 测试场景1: 红绿灯1是绿灯
        input("\n按回车键开始场景1测试（红绿灯1是绿灯）...")
        self.simulate_navigation_path("场景1: 红绿灯1是绿灯", True)
        
        # 测试场景2: 红绿灯1是红灯
        input("\n按回车键开始场景2测试（红绿灯1是红灯）...")
        self.simulate_navigation_path("场景2: 红绿灯1是红灯", False)
        
        print("\n🎉 所有测试完成！")
        print("\n📝 修正后的逻辑总结:")
        print("   ✅ 红绿灯1是绿灯 → 播放路口1音频 → 导航到路口1 → 结束")
        print("   ✅ 红绿灯1是红灯 → 导航到红绿灯2 → 播放路口2音频 → 导航到路口2 → 结束")
        print("   ✅ 红绿灯2不再检测状态，直接播放音频并前往路口2")

if __name__ == "__main__":
    tester = CorrectedTrafficLightLogicTester()
    tester.run_test()
